# 📁 Guia: Cópia de Arquivos Entre Servidores na Rede

## 📋 Índice
1. [<PERSON><PERSON><PERSON> Geral](#visão-geral)
2. [Pré-requisitos](#pré-requisitos)
3. [Método 1: CIFS/SMB (Windows Share)](#método-1-cifssmb-windows-share)
4. [Método 2: SCP (SSH)](#método-2-scp-ssh)
5. [Método 3: RSYNC](#método-3-rsync)
6. [Troubleshooting](#troubleshooting)
7. [<PERSON>as Práticas](#boas-práticas)

---

## 🎯 Visão Geral

Este guia documenta como copiar arquivos entre servidores na rede local usando diferentes protocolos e credenciais de autenticação.

### Cenário Exemplo:
- **Servidor Origem**: 10.19.208.3 (Proxmox PVE01)
- **Ser<PERSON><PERSON>**: *********** (Windows Server)
- **Arquivo**: template-ct104.tar.gz (4.6GB)
- **Compartilhamento**: \\***********\ti\17-TEMPLATES
- **Credenciais**: usuario: richardson, senha: 200381

---

## 🔧 Pré-requisitos

### No Servidor Linux (Origem):
```bash
# Instalar ferramentas necessárias
sudo apt update
sudo apt install -y cifs-utils openssh-client rsync

# Verificar conectividade
ping ***********
```

### No Servidor Windows (Destino):
- ✅ Compartilhamento de rede configurado
- ✅ Usuário com permissões de escrita
- ✅ Firewall liberado para SMB (porta 445)

---

## 📂 Método 1: CIFS/SMB (Windows Share)

### **Passo 1: Instalar CIFS Utils**
```bash
sudo apt update && sudo apt install -y cifs-utils
```

### **Passo 2: Criar Ponto de Montagem**
```bash
# Criar diretório para montagem
sudo mkdir -p /mnt/windows-share
```

### **Passo 3: Montar Compartilhamento**
```bash
# Montar com credenciais inline (menos seguro)
sudo mount -t cifs //***********/ti /mnt/windows-share \
    -o username=richardson,password=200381,uid=1000,gid=1000,iocharset=utf8

# OU usando arquivo de credenciais (mais seguro)
echo "username=richardson" | sudo tee /etc/cifs-credentials
echo "password=200381" | sudo tee -a /etc/cifs-credentials
sudo chmod 600 /etc/cifs-credentials

sudo mount -t cifs //***********/ti /mnt/windows-share \
    -o credentials=/etc/cifs-credentials,uid=1000,gid=1000,iocharset=utf8
```

### **Passo 4: Verificar Montagem**
```bash
# Verificar se montou corretamente
df -h | grep windows-share
ls -la /mnt/windows-share/

# Verificar pasta de destino
ls -la /mnt/windows-share/17-TEMPLATES/
```

### **Passo 5: Copiar Arquivo**
```bash
# Cópia simples
cp /var/lib/vz/template/cache/template-ct104.tar.gz /mnt/windows-share/17-TEMPLATES/

# OU cópia com progresso
rsync -avh --progress /var/lib/vz/template/cache/template-ct104.tar.gz /mnt/windows-share/17-TEMPLATES/

# OU usando pv para mostrar progresso
pv /var/lib/vz/template/cache/template-ct104.tar.gz > /mnt/windows-share/17-TEMPLATES/template-ct104.tar.gz
```

### **Passo 6: Verificar Cópia**
```bash
# Comparar tamanhos
ls -lh /var/lib/vz/template/cache/template-ct104.tar.gz
ls -lh /mnt/windows-share/17-TEMPLATES/template-ct104.tar.gz

# Verificar checksums (opcional)
md5sum /var/lib/vz/template/cache/template-ct104.tar.gz
md5sum /mnt/windows-share/17-TEMPLATES/template-ct104.tar.gz
```

### **Passo 7: Desmontar**
```bash
# Desmontar compartilhamento
sudo umount /mnt/windows-share

# Limpar credenciais (se usou arquivo)
sudo rm -f /etc/cifs-credentials
```

---

## 🔐 Método 2: SCP (SSH)

### **Pré-requisitos:**
- SSH habilitado no servidor destino
- Usuário com acesso SSH

### **Comando Básico:**
```bash
# Cópia via SCP
scp /var/lib/vz/template/cache/template-ct104.tar.gz usuario@***********:/caminho/destino/

# Com progresso (se disponível)
scp -v /var/lib/vz/template/cache/template-ct104.tar.gz usuario@***********:/caminho/destino/
```

### **Com Chave SSH:**
```bash
# Usando chave privada
scp -i ~/.ssh/id_rsa /var/lib/vz/template/cache/template-ct104.tar.gz usuario@***********:/caminho/destino/
```

---

## ⚡ Método 3: RSYNC

### **Via SSH:**
```bash
# RSYNC com SSH
rsync -avz --progress /var/lib/vz/template/cache/template-ct104.tar.gz usuario@***********:/caminho/destino/

# Com limitação de banda (exemplo: 50MB/s)
rsync -avz --progress --bwlimit=50000 /var/lib/vz/template/cache/template-ct104.tar.gz usuario@***********:/caminho/destino/
```

### **Via CIFS (após montar):**
```bash
# RSYNC local após montar CIFS
rsync -avh --progress /var/lib/vz/template/cache/template-ct104.tar.gz /mnt/windows-share/17-TEMPLATES/
```

---

## 🛠️ Troubleshooting

### **Problema: "mount error(13): Permission denied"**
```bash
# Soluções:
# 1. Verificar credenciais
# 2. Adicionar opções de versão SMB
sudo mount -t cifs //***********/ti /mnt/windows-share \
    -o username=richardson,password=200381,vers=2.0

# 3. Verificar se usuário tem permissões no Windows
```

### **Problema: "Host is down" ou timeout**
```bash
# Verificar conectividade
ping ***********
telnet *********** 445

# Verificar firewall
sudo ufw status
```

### **Problema: Cópia lenta via VPN**
```bash
# Verificar rotas
route -n
traceroute ***********

# Adicionar rota local específica
sudo ip route add ***********/24 dev eth0 metric 1
```

### **Problema: "No space left on device"**
```bash
# Verificar espaço em disco
df -h /mnt/windows-share/
df -h /var/lib/vz/

# Verificar inodes
df -i
```

---

## ✅ Boas Práticas

### **1. Segurança:**
```bash
# Usar arquivo de credenciais em vez de linha de comando
echo "username=usuario" > /tmp/creds
echo "password=senha" >> /tmp/creds
chmod 600 /tmp/creds

# Montar com arquivo
sudo mount -t cifs //servidor/share /mnt/ponto -o credentials=/tmp/creds

# Limpar após uso
rm /tmp/creds
```

### **2. Performance:**
```bash
# Opções de montagem para melhor performance
sudo mount -t cifs //servidor/share /mnt/ponto \
    -o credentials=/tmp/creds,rsize=65536,wsize=65536,cache=strict
```

### **3. Monitoramento:**
```bash
# Monitorar progresso com pv
pv arquivo_origem > /mnt/destino/arquivo_destino

# Monitorar largura de banda
iftop -i eth0

# Monitorar I/O
iostat -x 1
```

### **4. Verificação de Integridade:**
```bash
# Sempre verificar após cópia
md5sum arquivo_origem
md5sum arquivo_destino

# OU usar sha256
sha256sum arquivo_origem
sha256sum arquivo_destino
```

### **5. Script Automatizado:**
```bash
#!/bin/bash
# copy-to-windows.sh

SOURCE_FILE="$1"
DEST_SERVER="$2"
DEST_PATH="$3"
USERNAME="$4"
PASSWORD="$5"

# Validações
if [ $# -ne 5 ]; then
    echo "Uso: $0 <arquivo_origem> <servidor_destino> <caminho_destino> <usuario> <senha>"
    exit 1
fi

# Criar ponto de montagem
MOUNT_POINT="/tmp/mount_$$"
mkdir -p "$MOUNT_POINT"

# Montar
echo "Montando compartilhamento..."
mount -t cifs "//$DEST_SERVER/share" "$MOUNT_POINT" \
    -o username="$USERNAME",password="$PASSWORD"

# Copiar com progresso
echo "Copiando arquivo..."
rsync -avh --progress "$SOURCE_FILE" "$MOUNT_POINT/$DEST_PATH/"

# Verificar
echo "Verificando integridade..."
SOURCE_MD5=$(md5sum "$SOURCE_FILE" | cut -d' ' -f1)
DEST_MD5=$(md5sum "$MOUNT_POINT/$DEST_PATH/$(basename $SOURCE_FILE)" | cut -d' ' -f1)

if [ "$SOURCE_MD5" = "$DEST_MD5" ]; then
    echo "✅ Cópia verificada com sucesso!"
else
    echo "❌ Erro: Checksums não conferem!"
fi

# Limpar
umount "$MOUNT_POINT"
rmdir "$MOUNT_POINT"
```

---

## 📊 Exemplo Completo Executado

### **Comando Usado:**
```bash
# 1. Instalar ferramentas
apt update && apt install -y cifs-utils

# 2. Criar ponto de montagem
mkdir -p /mnt/templates

# 3. Montar compartilhamento
mount -t cifs //***********/ti /mnt/templates \
    -o username=richardson,password=200381

# 4. Verificar montagem
ls -la /mnt/templates/17-TEMPLATES/

# 5. Copiar arquivo
cp /var/lib/vz/template/cache/template-ct104.tar.gz /mnt/templates/17-TEMPLATES/

# 6. Verificar cópia
ls -lh /mnt/templates/17-TEMPLATES/template-ct104.tar.gz

# 7. Desmontar
umount /mnt/templates
```

### **Resultado:**
- ✅ Arquivo copiado: template-ct104.tar.gz (4.6GB)
- ✅ Localização: \\***********\ti\17-TEMPLATES\
- ✅ Integridade verificada
- ✅ Compartilhamento desmontado

---

**Documento criado em**: $(date)
**Autor**: Augment Agent
**Versão**: 1.0
