[wow-site]

; Pool name
user = www-data
group = www-data

; Socket configuration
listen = /var/run/php/php8.1-fpm-wow.sock
listen.owner = www-data
listen.group = www-data
listen.mode = 0660

; Process management
pm = dynamic
pm.max_children = 20
pm.start_servers = 4
pm.min_spare_servers = 2
pm.max_spare_servers = 6
pm.max_requests = 1000

; Performance tuning
request_terminate_timeout = 60s
request_slowlog_timeout = 10s
slowlog = /var/log/php8.1-fpm-wow-slow.log

; Security
security.limit_extensions = .php

; Environment variables
env[HOSTNAME] = $HOSTNAME
env[PATH] = /usr/local/bin:/usr/bin:/bin
env[TMP] = /tmp
env[TMPDIR] = /tmp
env[TEMP] = /tmp

; PHP configuration
php_admin_value[sendmail_path] = /usr/sbin/sendmail -t -i -f <EMAIL>
php_flag[display_errors] = off
php_admin_value[error_log] = /var/log/php8.1-fpm-wow-error.log
php_admin_flag[log_errors] = on
php_admin_value[memory_limit] = 256M
php_admin_value[max_execution_time] = 60
php_admin_value[max_input_time] = 60
php_admin_value[post_max_size] = 50M
php_admin_value[upload_max_filesize] = 50M
php_admin_value[max_file_uploads] = 20

; Session configuration
php_value[session.save_handler] = files
php_value[session.save_path] = /var/lib/php/sessions
php_value[session.use_cookies] = 1
php_value[session.cookie_secure] = 1
php_value[session.cookie_httponly] = 1
php_value[session.use_strict_mode] = 1

; Security settings
php_admin_value[open_basedir] = /var/www/html:/tmp:/var/lib/php/sessions
php_admin_value[disable_functions] = exec,passthru,shell_exec,system,proc_open,popen,curl_exec,curl_multi_exec,parse_ini_file,show_source
