<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AzerothCore Server - Demo</title>
    <meta name="description" content="Servidor World of Warcraft 3.3.5a - A melhor experiÃªncia WotLK">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="assets/css/style.css" rel="stylesheet">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="assets/images/favicon.ico">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark fixed-top">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-shield-alt me-2"></i>
                AzerothCore Server
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="/">InÃ­cio</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#download">Download</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#ranking">Ranking</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#forum">FÃ³rum</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#donate">DoaÃ§Ãµes</a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="#login">Login</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#register">Registrar</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-section">
        <div class="hero-overlay"></div>
        <div class="container">
            <div class="row align-items-center min-vh-100">
                <div class="col-lg-6">
                    <div class="hero-content text-white">
                        <h1 class="display-4 fw-bold mb-4">
                            Bem-vindo ao AzerothCore Server
                        </h1>
                        <p class="lead mb-4">
                            Experimente a melhor aventura em World of Warcraft 3.3.5a. 
                            Servidor estÃ¡vel, comunidade ativa e diversÃ£o garantida!
                        </p>
                        <div class="hero-buttons">
                            <a href="#register" class="btn btn-primary btn-lg me-3">
                                <i class="fas fa-user-plus"></i> Criar Conta
                            </a>
                            <a href="#download" class="btn btn-outline-light btn-lg">
                                <i class="fas fa-download"></i> Download
                            </a>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="server-status-card">
                        <div class="card bg-dark text-white">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-server"></i> Status do Servidor
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row text-center">
                                    <div class="col-6">
                                        <div class="stat-item">
                                            <div class="stat-value">
                                                <span class="text-success">
                                                    <i class="fas fa-circle"></i> ONLINE
                                                </span>
                                            </div>
                                            <div class="stat-label">Status</div>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="stat-item">
                                            <div class="stat-value text-primary" data-stat="players-online">
                                                247
                                            </div>
                                            <div class="stat-label">Jogadores Online</div>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="stat-item">
                                            <div class="stat-value text-info" data-stat="total-accounts">
                                                244
                                            </div>
                                            <div class="stat-label">Contas Registradas</div>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="stat-item">
                                            <div class="stat-value text-warning" data-stat="uptime">
                                                24h 30m
                                            </div>
                                            <div class="stat-label">Uptime</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="features-section py-5">
        <div class="container">
            <div class="row">
                <div class="col-lg-12 text-center mb-5">
                    <h2 class="section-title">Por que escolher nosso servidor?</h2>
                    <p class="section-subtitle">CaracterÃ­sticas que nos tornam Ãºnicos</p>
                </div>
            </div>
            <div class="row">
                <div class="col-md-4 mb-4">
                    <div class="feature-card text-center">
                        <div class="feature-icon">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <h4>Estabilidade</h4>
                        <p>Servidor com 99.9% de uptime, backups automÃ¡ticos e infraestrutura robusta.</p>
                    </div>
                </div>
                <div class="col-md-4 mb-4">
                    <div class="feature-card text-center">
                        <div class="feature-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <h4>Comunidade Ativa</h4>
                        <p>Milhares de jogadores ativos, eventos regulares e suporte 24/7.</p>
                    </div>
                </div>
                <div class="col-md-4 mb-4">
                    <div class="feature-card text-center">
                        <div class="feature-icon">
                            <i class="fas fa-cog"></i>
                        </div>
                        <h4>Rates Balanceadas</h4>
                        <p>XP: 1x | Drop: 1x | ExperiÃªncia autÃªntica Blizzlike.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Top Players Section -->
    <section class="top-players-section py-5 bg-light">
        <div class="container">
            <div class="row">
                <div class="col-lg-12 text-center mb-5">
                    <h2 class="section-title">Top Jogadores</h2>
                    <p class="section-subtitle">Os herÃ³is mais poderosos do servidor</p>
                </div>
            </div>
            <div class="row">
                <div class="col-lg-8 mx-auto">
                    <div class="card">
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Rank</th>
                                            <th>Nome</th>
                                            <th>Level</th>
                                            <th>Classe</th>
                                            <th>RaÃ§a</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td><span class="rank-badge rank-1">#1</span></td>
                                            <td><strong style="color: #C79C6E">Arthas</strong></td>
                                            <td><span class="badge bg-primary">80</span></td>
                                            <td>Cavaleiro da Morte</td>
                                            <td><span class="faction-alliance">Humano</span></td>
                                        </tr>
                                        <tr>
                                            <td><span class="rank-badge rank-2">#2</span></td>
                                            <td><strong style="color: #F58CBA">Jaina</strong></td>
                                            <td><span class="badge bg-primary">80</span></td>
                                            <td>Mago</td>
                                            <td><span class="faction-alliance">Humano</span></td>
                                        </tr>
                                        <tr>
                                            <td><span class="rank-badge rank-3">#3</span></td>
                                            <td><strong style="color: #ABD473">Sylvanas</strong></td>
                                            <td><span class="badge bg-primary">80</span></td>
                                            <td>CaÃ§ador</td>
                                            <td><span class="faction-horde">Morto-vivo</span></td>
                                        </tr>
                                        <tr>
                                            <td><span class="rank-badge rank-4">#4</span></td>
                                            <td><strong style="color: #0070DE">Thrall</strong></td>
                                            <td><span class="badge bg-primary">79</span></td>
                                            <td>XamÃ£</td>
                                            <td><span class="faction-horde">Orc</span></td>
                                        </tr>
                                        <tr>
                                            <td><span class="rank-badge rank-5">#5</span></td>
                                            <td><strong style="color: #9482C9">Guldan</strong></td>
                                            <td><span class="badge bg-primary">78</span></td>
                                            <td>Bruxo</td>
                                            <td><span class="faction-horde">Orc</span></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <div class="text-center mt-3">
                                <a href="#ranking" class="btn btn-primary">Ver Ranking Completo</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer bg-dark text-white py-4">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5>AzerothCore Server</h5>
                    <p>O melhor servidor World of Warcraft 3.3.5a do Brasil.</p>
                    <p><small>ðŸŽ® <strong>DEMO VERSION</strong> - Site em desenvolvimento</small></p>
                </div>
                <div class="col-md-6 text-md-end">
                    <div class="social-links">
                        <a href="#" class="text-white me-3"><i class="fab fa-discord"></i></a>
                        <a href="#" class="text-white me-3"><i class="fab fa-facebook"></i></a>
                        <a href="#" class="text-white"><i class="fab fa-youtube"></i></a>
                    </div>
                    <p class="mt-2 mb-0">
                        &copy; 2025 AzerothCore Server. Todos os direitos reservados.
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="assets/js/main.js"></script>
    
</body>
</html>
    <!-- Demo Script -->
    <script>
        // Buscar estatÃ­sticas reais do servidor
        async function updateStats() {
            try {
                const response = await fetch("api/stats.php");
                const data = await response.json();
                
                // A API retorna os dados diretamente, nÃ£o em data.success
                if (data.online !== undefined) {
                    document.querySelector('[data-stat="players-online"]').textContent = data.players_online || 0;
                    document.querySelector('[data-stat="total-accounts"]').textContent = data.total_accounts || 0;
                    document.querySelector('[data-stat="uptime"]').textContent = data.uptime || "Offline";
                }
            } catch (error) {
                console.error("Erro ao buscar estatÃ­sticas:", error);
                // Fallback para valores padrÃ£o em caso de erro
                document.querySelector('[data-stat="players-online"]').textContent = "0";
                document.querySelector('[data-stat="total-accounts"]').textContent = "0";
                document.querySelector('[data-stat="uptime"]').textContent = "Offline";
            }
        }
        
        // Atualizar estatÃ­sticas imediatamente e depois a cada 30 segundos
        updateStats();
        setInterval(updateStats, 30000);

    </script>
</body>
</html>
