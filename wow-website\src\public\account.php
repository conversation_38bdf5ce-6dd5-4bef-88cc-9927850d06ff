<?php
/**
 * Painel de Gerenciamento da Conta
 * 
 * <AUTHOR> Agent
 * @version 1.0
 */

require_once '../includes/config/config.php';

// Verificar se está logado
if (!isLoggedIn()) {
    redirect('/login?redirect=' . urlencode('/account'));
}

// Obter dados da conta
function getAccountData($user_id) {
    try {
        $auth_db = DatabaseManager::getConnection('auth');
        $stmt = $auth_db->prepare("
            SELECT id, username, email, joindate, last_login, last_ip, 
                   expansion, gmlevel, locked, failed_logins
            FROM account 
            WHERE id = ?
        ");
        $stmt->execute([$user_id]);
        return $stmt->fetch();
    } catch (Exception $e) {
        return null;
    }
}

// Obter personagens da conta
function getAccountCharacters($user_id) {
    try {
        $char_db = DatabaseManager::getConnection('characters');
        $stmt = $char_db->prepare("
            SELECT name, level, race, class, zone, totaltime, 
                   DATE_FORMAT(logout_time, '%d/%m/%Y %H:%i') as last_logout,
                   online
            FROM characters 
            WHERE account = ?
            ORDER BY level DESC, totaltime DESC
        ");
        $stmt->execute([$user_id]);
        return $stmt->fetchAll();
    } catch (Exception $e) {
        return [];
    }
}

$account_data = getAccountData($_SESSION['user_id']);
$characters = getAccountCharacters($_SESSION['user_id']);

if (!$account_data) {
    redirect('/logout');
}

// Dados para o template
$page_data = [
    'title' => 'Minha Conta - ' . SITE_NAME,
    'description' => 'Painel de gerenciamento da conta',
    'current_page' => 'account'
];

?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= escape($page_data['title']) ?></title>
    <meta name="description" content="<?= escape($page_data['description']) ?>">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="<?= ASSETS_PATH ?>/css/style.css" rel="stylesheet">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?= ASSETS_PATH ?>/images/favicon.ico">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark fixed-top">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-shield-alt me-2"></i>
                <?= SITE_NAME ?>
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">Início</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/download">Download</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/ranking">Ranking</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/forum">Fórum</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/donate">Doações</a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle active" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user"></i> <?= escape($_SESSION['username']) ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item active" href="/account">Minha Conta</a></li>
                            <li><a class="dropdown-item" href="/characters">Personagens</a></li>
                            <?php if (isAdmin()): ?>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="/admin">Painel Admin</a></li>
                            <?php endif; ?>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="/logout">Sair</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container mt-5 pt-5">
        <div class="row">
            <div class="col-12">
                <h1 class="mb-4">
                    <i class="fas fa-user-circle text-primary"></i> 
                    Painel da Conta
                </h1>
            </div>
        </div>
        
        <div class="row">
            <!-- Informações da Conta -->
            <div class="col-lg-4 mb-4">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-info-circle"></i> Informações da Conta
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label text-muted">Nome de Usuário</label>
                            <div class="fw-bold"><?= escape($account_data['username']) ?></div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label text-muted">Email</label>
                            <div><?= escape($account_data['email']) ?></div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label text-muted">Data de Registro</label>
                            <div><?= date('d/m/Y H:i', strtotime($account_data['joindate'])) ?></div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label text-muted">Último Login</label>
                            <div>
                                <?php if ($account_data['last_login']): ?>
                                    <?= date('d/m/Y H:i', strtotime($account_data['last_login'])) ?>
                                <?php else: ?>
                                    Primeiro login
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label text-muted">Status da Conta</label>
                            <div>
                                <?php if ($account_data['locked']): ?>
                                    <span class="badge bg-danger">Bloqueada</span>
                                <?php else: ?>
                                    <span class="badge bg-success">Ativa</span>
                                <?php endif; ?>
                                
                                <?php if ($account_data['gmlevel'] > 0): ?>
                                    <span class="badge bg-warning ms-1">Staff</span>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label text-muted">Expansão</label>
                            <div>
                                <?php
                                $expansions = [
                                    0 => 'Classic',
                                    1 => 'The Burning Crusade',
                                    2 => 'Wrath of the Lich King'
                                ];
                                echo $expansions[$account_data['expansion']] ?? 'Desconhecida';
                                ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Estatísticas -->
            <div class="col-lg-4 mb-4">
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-bar"></i> Estatísticas
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-6 mb-3">
                                <div class="stat-item">
                                    <div class="stat-value text-primary h4">
                                        <?= count($characters) ?>
                                    </div>
                                    <div class="stat-label text-muted">Personagens</div>
                                </div>
                            </div>
                            <div class="col-6 mb-3">
                                <div class="stat-item">
                                    <div class="stat-value text-success h4">
                                        <?php
                                        $max_level = 0;
                                        foreach ($characters as $char) {
                                            if ($char['level'] > $max_level) {
                                                $max_level = $char['level'];
                                            }
                                        }
                                        echo $max_level;
                                        ?>
                                    </div>
                                    <div class="stat-label text-muted">Level Máximo</div>
                                </div>
                            </div>
                            <div class="col-6 mb-3">
                                <div class="stat-item">
                                    <div class="stat-value text-warning h4">
                                        <?php
                                        $total_time = 0;
                                        foreach ($characters as $char) {
                                            $total_time += $char['totaltime'];
                                        }
                                        echo formatPlayTime($total_time);
                                        ?>
                                    </div>
                                    <div class="stat-label text-muted">Tempo Total</div>
                                </div>
                            </div>
                            <div class="col-6 mb-3">
                                <div class="stat-item">
                                    <div class="stat-value text-info h4">
                                        <?php
                                        $online_count = 0;
                                        foreach ($characters as $char) {
                                            if ($char['online']) $online_count++;
                                        }
                                        echo $online_count;
                                        ?>
                                    </div>
                                    <div class="stat-label text-muted">Online Agora</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Ações Rápidas -->
                <div class="card mt-3">
                    <div class="card-header bg-secondary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-tools"></i> Ações Rápidas
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <a href="/change-password" class="btn btn-outline-primary">
                                <i class="fas fa-key"></i> Alterar Senha
                            </a>
                            <a href="/change-email" class="btn btn-outline-info">
                                <i class="fas fa-envelope"></i> Alterar Email
                            </a>
                            <a href="/download" class="btn btn-outline-success">
                                <i class="fas fa-download"></i> Download Cliente
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Lista de Personagens -->
            <div class="col-lg-4 mb-4">
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-users"></i> Meus Personagens
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($characters)): ?>
                            <div class="text-center text-muted">
                                <i class="fas fa-user-plus fa-3x mb-3"></i>
                                <p>Você ainda não criou nenhum personagem.</p>
                                <a href="/download" class="btn btn-primary btn-sm">
                                    <i class="fas fa-download"></i> Baixar Cliente
                                </a>
                            </div>
                        <?php else: ?>
                            <div class="character-list">
                                <?php foreach ($characters as $char): ?>
                                    <div class="character-item border-bottom pb-2 mb-2">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <div class="fw-bold" style="color: <?= getClassColor($char['class']) ?>">
                                                    <?= escape($char['name']) ?>
                                                    <?php if ($char['online']): ?>
                                                        <span class="badge bg-success ms-1">Online</span>
                                                    <?php endif; ?>
                                                </div>
                                                <small class="text-muted">
                                                    Level <?= $char['level'] ?> <?= getClassName($char['class']) ?> 
                                                    (<?= getRaceName($char['race']) ?>)
                                                </small>
                                            </div>
                                            <div class="text-end">
                                                <small class="text-muted d-block">
                                                    <?= formatPlayTime($char['totaltime']) ?>
                                                </small>
                                                <small class="text-muted">
                                                    <?= $char['last_logout'] ?: 'Nunca' ?>
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
