# 🎮 AzerothNexus Website

## 📋 Visão Geral

Site profissional para servidor World of Warcraft 3.3.5a AzerothNexus.

### 🛠️ Stack Tecnológico
- **Frontend**: HTML5, CSS3, JavaScript (Vanilla)
- **Backend**: PHP 8.1+
- **Servidor Web**: Nginx
- **Banco de Dados**: MySQL 8.0+ (AzerothCore)
- **Cache**: Redis (opcional)
- **Deploy**: Automated via SSH

### 🗄️ Estrutura de Bancos de Dados
- **auth**: Contas de usuário, autenticação
- **characters**: Personagens dos jogadores
- **world**: Dados do mundo (NPCs, quests, itens)

## 📁 Estrutura do Projeto

```
wow-website/
├── README.md
├── deploy/                     # Scripts de deploy
│   ├── deploy.sh
│   ├── nginx.conf
│   └── php-fpm.conf
├── src/                        # Código fonte
│   ├── public/                 # Arquivos públicos
│   │   ├── index.php
│   │   ├── assets/
│   │   │   ├── css/
│   │   │   ├── js/
│   │   │   └── images/
│   │   ├── api/                # API endpoints
│   │   └── admin/              # Painel administrativo
│   ├── includes/               # Arquivos PHP incluídos
│   │   ├── config/
│   │   ├── classes/
│   │   └── functions/
│   └── templates/              # Templates HTML
├── database/                   # Scripts de banco
│   ├── migrations/
│   └── seeds/
└── docs/                      # Documentação
```

## 🚀 Funcionalidades

### 👥 **Para Jogadores**
- ✅ Registro de conta
- ✅ Login/Logout
- ✅ Painel do jogador
- ✅ Visualização de personagens
- ✅ Ranking de jogadores
- ✅ Estatísticas do servidor
- ✅ Download do cliente
- ✅ Guias e tutoriais

### 🛡️ **Para Administradores**
- ✅ Painel administrativo
- ✅ Gerenciamento de contas
- ✅ Monitoramento do servidor
- ✅ Logs de atividade
- ✅ Configurações do site

### 📊 **Recursos Avançados**
- ✅ Sistema de doações
- ✅ Loja virtual (itens cosméticos)
- ✅ Fórum integrado
- ✅ Sistema de tickets
- ✅ Notificações em tempo real

## 🔧 Instalação

### Pré-requisitos
- Ubuntu 22.04 LTS
- Nginx 1.18+
- PHP 8.1+ com extensões: mysqli, pdo, json, curl
- MySQL 8.0+
- Composer
- Node.js (para build de assets)

### Deploy Automatizado
```bash
# 1. Configurar SSH sem senha (será feito após container estar pronto)
# 2. Executar deploy
./deploy/deploy.sh production
```

## 🔐 Segurança

- ✅ Validação de entrada
- ✅ Proteção CSRF
- ✅ Sanitização de dados
- ✅ Rate limiting
- ✅ SSL/TLS obrigatório
- ✅ Headers de segurança

## 📱 Responsividade

- ✅ Mobile-first design
- ✅ Compatível com todos os dispositivos
- ✅ Performance otimizada

## 🎨 Design

Inspirado nos melhores servidores privados:
- **Warmane**: Layout profissional e limpo
- **Dalaran-WoW**: Interface intuitiva
- **Icecrown**: Design moderno e responsivo

## 📈 Performance

- ✅ Cache de queries
- ✅ Compressão de assets
- ✅ CDN ready
- ✅ Lazy loading
- ✅ Minificação CSS/JS

## 🔄 Deploy

### Ambientes
- **development**: Desenvolvimento local
- **staging**: Testes
- **production**: Produção

### Processo
1. Build dos assets
2. Upload via SSH
3. Configuração do Nginx
4. Restart dos serviços
5. Verificação de saúde

## 📞 Suporte

- **Discord**: Link do servidor
- **Email**: <EMAIL>
- **Tickets**: Sistema integrado

---

**Desenvolvido para AzerothNexus WotLK 3.3.5a**
