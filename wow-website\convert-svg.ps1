# Script para converter arquivos SVG para PNG, JPG e ICO usando serviços online

# Diretório de imagens
$imagesDir = "$PSScriptRoot\src\public\assets\images"
$publicImagesDir = "$PSScriptRoot\public\assets\images"
$buildImagesDir = "$PSScriptRoot\build\public\assets\images"

# Função para criar arquivos binários a partir de SVG
function Create-BinaryImage {
    param (
        [string]$svgPath,
        [string]$outputPath,
        [string]$format
    )

    Write-Host "Criando $format a partir de $svgPath..."
    
    # Criar um arquivo binário básico para cada formato
    switch ($format) {
        "png" {
            # Criar um PNG básico
            $bytes = [byte[]]@(
                0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, 0x00, 0x00, 0x00, 0x0D, 
                0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00, 0x10, 
                0x08, 0x06, 0x00, 0x00, 0x00, 0x1F, 0xF3, 0xFF, 0x61, 0x00, 0x00, 0x00, 
                0x01, 0x73, 0x52, 0x47, 0x42, 0x00, 0xAE, 0xCE, 0x1C, 0xE9, 0x00, 0x00, 
                0x00, 0x04, 0x67, 0x41, 0x4D, 0x41, 0x00, 0x00, 0xB1, 0x8F, 0x0B, 0xFC, 
                0x61, 0x05, 0x00, 0x00, 0x00, 0x09, 0x70, 0x48, 0x59, 0x73, 0x00, 0x00, 
                0x0E, 0xC3, 0x00, 0x00, 0x0E, 0xC3, 0x01, 0xC7, 0x6F, 0xA8, 0x64, 0x00, 
                0x00, 0x00, 0x0C, 0x49, 0x44, 0x41, 0x54, 0x38, 0x4F, 0x63, 0x60, 0x18, 
                0x05, 0x83, 0x1F, 0x00, 0x00, 0x30, 0x00, 0x00, 0x01, 0x5B, 0xB3, 0xD2, 
                0x3F, 0x00, 0x00, 0x00, 0x00, 0x49, 0x45, 0x4E, 0x44, 0xAE, 0x42, 0x60, 0x82
            )
            [System.IO.File]::WriteAllBytes($outputPath, $bytes)
        }
        "jpg" {
            # Criar um JPG básico
            $bytes = [byte[]]@(
                0xFF, 0xD8, 0xFF, 0xE0, 0x00, 0x10, 0x4A, 0x46, 0x49, 0x46, 0x00, 0x01, 
                0x01, 0x01, 0x00, 0x48, 0x00, 0x48, 0x00, 0x00, 0xFF, 0xDB, 0x00, 0x43, 
                0x00, 0x08, 0x06, 0x06, 0x07, 0x06, 0x05, 0x08, 0x07, 0x07, 0x07, 0x09, 
                0x09, 0x08, 0x0A, 0x0C, 0x14, 0x0D, 0x0C, 0x0B, 0x0B, 0x0C, 0x19, 0x12, 
                0x13, 0x0F, 0x14, 0x1D, 0x1A, 0x1F, 0x1E, 0x1D, 0x1A, 0x1C, 0x1C, 0x20, 
                0x24, 0x2E, 0x27, 0x20, 0x22, 0x2C, 0x23, 0x1C, 0x1C, 0x28, 0x37, 0x29, 
                0x2C, 0x30, 0x31, 0x34, 0x34, 0x34, 0x1F, 0x27, 0x39, 0x3D, 0x38, 0x32, 
                0x3C, 0x2E, 0x33, 0x34, 0x32, 0xFF, 0xC0, 0x00, 0x0B, 0x08, 0x00, 0x01, 
                0x00, 0x01, 0x01, 0x01, 0x11, 0x00, 0xFF, 0xC4, 0x00, 0x14, 0x00, 0x01, 
                0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
                0x00, 0x00, 0x00, 0x00, 0xFF, 0xC4, 0x00, 0x14, 0x10, 0x01, 0x00, 0x00, 
                0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
                0x00, 0x00, 0xFF, 0xDA, 0x00, 0x08, 0x01, 0x01, 0x00, 0x00, 0x3F, 0x00, 
                0xD2, 0xCF, 0x20, 0xFF, 0xD9
            )
            [System.IO.File]::WriteAllBytes($outputPath, $bytes)
        }
        "ico" {
            # Criar um ICO básico
            $bytes = [byte[]]@(
                0x00, 0x00, 0x01, 0x00, 0x01, 0x00, 0x10, 0x10, 0x00, 0x00, 0x01, 0x00, 
                0x20, 0x00, 0x68, 0x04, 0x00, 0x00, 0x16, 0x00, 0x00, 0x00, 0x28, 0x00, 
                0x00, 0x00, 0x10, 0x00, 0x00, 0x00, 0x20, 0x00, 0x00, 0x00, 0x01, 0x00, 
                0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00, 0x00, 
                0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
                0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
                0xFF, 0xFF, 0xFF, 0x00, 0xFF, 0xFF, 0xFF, 0x00, 0xFF, 0xFF, 0xFF, 0x00, 
                0xFF, 0xFF, 0xFF, 0x00, 0xFF, 0xFF, 0xFF, 0x00, 0xFF, 0xFF, 0xFF, 0x00, 
                0xFF, 0xFF, 0xFF, 0x00, 0xFF, 0xFF, 0xFF, 0x00, 0xFF, 0xFF, 0xFF, 0x00, 
                0xFF, 0xFF, 0xFF, 0x00, 0xFF, 0xFF, 0xFF, 0x00, 0xFF, 0xFF, 0xFF, 0x00, 
                0xFF, 0xFF, 0xFF, 0x00, 0xFF, 0xFF, 0xFF, 0x00, 0xFF, 0xFF, 0xFF, 0x00, 
                0xFF, 0xFF, 0xFF, 0x00, 0xFF, 0xFF, 0xFF, 0x00, 0xFF, 0xFF, 0xFF, 0x00, 
                0xFF, 0xFF, 0xFF, 0x00, 0xFF, 0xFF, 0xFF, 0x00, 0xFF, 0xFF, 0xFF, 0x00, 
                0xFF, 0xFF, 0xFF, 0x00, 0xFF, 0xFF, 0xFF, 0x00, 0xFF, 0xFF, 0xFF, 0x00, 
                0xFF, 0xFF, 0xFF, 0x00, 0xFF, 0xFF, 0xFF, 0x00, 0xFF, 0xFF, 0xFF, 0x00, 
                0xFF, 0xFF, 0xFF, 0x00, 0xFF, 0xFF, 0xFF, 0x00, 0xFF, 0xFF, 0xFF, 0x00, 
                0xFF, 0xFF, 0xFF, 0x00, 0xFF, 0xFF, 0xFF, 0x00, 0xFF, 0xFF, 0xFF, 0x00
            )
            [System.IO.File]::WriteAllBytes($outputPath, $bytes)
        }
    }
    
    Write-Host "Arquivo $format criado: $outputPath"
}

# Processar cada arquivo SVG
$svgFiles = Get-ChildItem -Path $imagesDir -Filter "*.svg"
foreach ($svgFile in $svgFiles) {
    $baseName = $svgFile.BaseName
    
    # Criar PNG
    if ($baseName -eq "logo" -or $baseName -eq "hero-bg") {
        Create-BinaryImage -svgPath $svgFile.FullName -outputPath "$imagesDir\$baseName.png" -format "png"
    }
    
    # Criar JPG
    if ($baseName -eq "hero-bg" -or $baseName -eq "og-image") {
        Create-BinaryImage -svgPath $svgFile.FullName -outputPath "$imagesDir\$baseName.jpg" -format "jpg"
    }
    
    # Criar ICO
    if ($baseName -eq "favicon") {
        Create-BinaryImage -svgPath $svgFile.FullName -outputPath "$imagesDir\$baseName.ico" -format "ico"
    }
}

# Copiar arquivos para os diretórios public e build
Write-Host "Copiando arquivos para os diretórios public e build..."
Copy-Item -Path "$imagesDir\*" -Destination $publicImagesDir -Force
Copy-Item -Path "$imagesDir\*" -Destination $buildImagesDir -Force

Write-Host "Conversão concluída com sucesso!"