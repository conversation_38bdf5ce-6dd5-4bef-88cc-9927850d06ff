<?php
/**
 * <PERSON><PERSON> de <PERSON> via Web
 */

// Primeiro, obter o token CSRF
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://localhost/login.php');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HEADER, true);
$response = curl_exec($ch);
$header_size = curl_getinfo($ch, CURLINFO_HEADER_SIZE);
$headers = substr($response, 0, $header_size);
$body = substr($response, $header_size);
curl_close($ch);

// Extrair CSRF token
preg_match('/name="csrf_token" value="([^"]+)"/', $body, $matches);
$csrf_token = $matches[1] ?? '';

// Extrair cookies de sessão
preg_match('/Set-Cookie: PHPSESSID=([^;]+)/', $headers, $cookie_matches);
$session_id = $cookie_matches[1] ?? '';

echo "CSRF Token: $csrf_token\n";
echo "Session ID: $session_id\n";

if (empty($csrf_token) || empty($session_id)) {
    echo "❌ Erro ao obter token CSRF ou sessão\n";
    exit;
}

// Agora fazer login com token válido
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://localhost/login.php');
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query([
    'username' => 'cavalcrod',
    'password' => '200381',
    'csrf_token' => $csrf_token
]));
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HEADER, true);
curl_setopt($ch, CURLOPT_COOKIE, "PHPSESSID=$session_id");
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, false);

$response = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$header_size = curl_getinfo($ch, CURLINFO_HEADER_SIZE);
$headers = substr($response, 0, $header_size);
curl_close($ch);

echo "HTTP Code: $http_code\n";

if ($http_code == 302) {
    preg_match('/Location: (.+)/', $headers, $location_matches);
    $redirect_url = trim($location_matches[1] ?? '');
    echo "✅ LOGIN SUCESSO! Redirecionando para: $redirect_url\n";
} else {
    echo "❌ LOGIN FALHOU! Código HTTP: $http_code\n";
    echo "Headers:\n$headers\n";
}
?>
