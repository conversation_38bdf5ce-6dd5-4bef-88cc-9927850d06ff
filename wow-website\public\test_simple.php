<?php
/**
 * Teste Simples - Verificação Básica
 */

echo "<h2>🔍 Teste Simples</h2>\n";
echo "<p>✅ PHP está funcionando!</p>\n";

// Verificar se o arquivo de configuração existe
$config_file = '../includes/config/config.php';
echo "<p>Verificando arquivo: $config_file</p>\n";

if (file_exists($config_file)) {
    echo "<p>✅ Arquivo de configuração encontrado!</p>\n";
    
    try {
        require_once $config_file;
        echo "<p>✅ Arquivo de configuração carregado!</p>\n";
        
        // Testar conexão com banco
        if (class_exists('DatabaseManager')) {
            echo "<p>✅ Classe DatabaseManager disponível!</p>\n";
            
            try {
                $auth_db = DatabaseManager::getConnection('auth');
                echo "<p>✅ Conexão com banco estabelecida!</p>\n";
                
                // Teste simples de query
                $stmt = $auth_db->prepare("SELECT COUNT(*) as total FROM account");
                $stmt->execute();
                $result = $stmt->fetch();
                echo "<p>✅ Total de contas no banco: " . $result['total'] . "</p>\n";
                
            } catch (Exception $e) {
                echo "<p style='color: red;'>❌ Erro de banco: " . htmlspecialchars($e->getMessage()) . "</p>\n";
            }
        } else {
            echo "<p style='color: red;'>❌ Classe DatabaseManager não encontrada!</p>\n";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Erro ao carregar configuração: " . htmlspecialchars($e->getMessage()) . "</p>\n";
    }
} else {
    echo "<p style='color: red;'>❌ Arquivo de configuração não encontrado!</p>\n";
    
    // Listar arquivos disponíveis
    echo "<h3>Arquivos disponíveis:</h3>\n";
    $files = glob('../*');
    foreach ($files as $file) {
        echo "<p>" . htmlspecialchars($file) . "</p>\n";
    }
}

echo "<hr>\n";
echo "<p><a href='index.php'>← Voltar para Home</a></p>\n";
?>
