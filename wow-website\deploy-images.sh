#!/bin/bash

# Script para fazer o deploy das imagens para o servidor
# Este script deve ser executado após a criação das imagens

# Configurações
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
ENVIRONMENT=${1:-staging}

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Funções
log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}✅ $1${NC}"
}

warning() {
    echo -e "${YELLOW}⚠️ $1${NC}"
}

error() {
    echo -e "${RED}❌ $1${NC}"
    exit 1
}

# Configurações por ambiente
case $ENVIRONMENT in
    "development")
        SERVER_HOST="localhost"
        SERVER_USER="www-data"
        SERVER_PATH="/var/www/html/wow-dev"
        SSH_KEY=""
        ;;
    "staging")
        SERVER_HOST="************"  # IP do servidor Azeroth-Nexus
        SERVER_USER="root"
        SERVER_PATH="/var/www/html/wow-staging"
        SSH_KEY="$HOME/.ssh/azeroth-nexus"
        ;;
    "production")
        SERVER_HOST="************"  # IP do servidor Azeroth-Nexus
        SERVER_USER="root"
        SERVER_PATH="/var/www/html"
        SSH_KEY="$HOME/.ssh/azeroth-nexus"
        ;;
    *)
        error "Ambiente inválido: $ENVIRONMENT. Use: development, staging, production"
        ;;
esac

# Verificar se estamos no diretório correto
if [ ! -d "$SCRIPT_DIR/src/public/assets/images" ]; then
    error "Diretório de imagens não encontrado. Execute o script do diretório correto."
fi

# Verificar dependências
command -v rsync >/dev/null 2>&1 || error "rsync não está instalado"
command -v ssh >/dev/null 2>&1 || error "ssh não está instalado"

# Função para fazer o deploy das imagens
deploy_images() {
    log "🖼️ Fazendo deploy das imagens para $SERVER_HOST..."
    
    # Testar conexão SSH
    if ! ssh -i "$SSH_KEY" -o ConnectTimeout=10 "$SERVER_USER@$SERVER_HOST" "echo 'Conexão OK'" >/dev/null 2>&1; then
        warning "Não foi possível conectar ao servidor remoto"
        warning "Verifique se o container está rodando e a chave SSH está configurada"
        return 0
    fi
    
    # Criar diretório de imagens no servidor
    log "📁 Criando diretório de imagens no servidor..."
    ssh -i "$SSH_KEY" "$SERVER_USER@$SERVER_HOST" "mkdir -p '$SERVER_PATH/public/assets/images'"
    
    # Upload das imagens
    log "📤 Enviando imagens..."
    rsync -avz \
        -e "ssh -i $SSH_KEY" \
        "$SCRIPT_DIR/build/public/assets/images/" "$SERVER_USER@$SERVER_HOST:$SERVER_PATH/public/assets/images/"
    
    # Configurar permissões remotamente
    log "🔐 Configurando permissões..."
    ssh -i "$SSH_KEY" "$SERVER_USER@$SERVER_HOST" "
        sudo chown -R www-data:www-data '$SERVER_PATH/public/assets/images'
        sudo chmod -R 755 '$SERVER_PATH/public/assets/images'
    "
    
    # Verificar se as imagens foram transferidas corretamente
    log "🔍 Verificando imagens..."
    ssh -i "$SSH_KEY" "$SERVER_USER@$SERVER_HOST" "ls -la '$SERVER_PATH/public/assets/images/'"
    
    success "Deploy das imagens concluído com sucesso!"
}

# Executar o deploy das imagens
deploy_images