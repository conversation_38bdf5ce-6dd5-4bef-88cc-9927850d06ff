#!/bin/bash

# ===============================================================================
# SCRIPT: Gerador de Template LXC para AzerothCore
# AUTOR: Augment Agent
# VERSÃO: 2.0
# DATA: $(date +%Y-%m-%d)
#
# DESCRIÇÃO:
# Cria template LXC limpo do AzerothCore seguindo as regras do projeto
# - Verifica espaço em disco antes de iniciar
# - Remove configurações específicas (rede, SSH keys, logs)
# - Preserva apenas o AzerothCore e aplicações
# - Remove backup automaticamente para economizar espaço
#
# USO: ./gerar-template-lxc.sh <CTID>
# EXEMPLO: ./gerar-template-lxc.sh 104
#
# REGRAS SEGUIDAS:
# ✅ Foco no problema específico (criar template)
# ✅ Resolve na raiz (template limpo e funcional)
# ✅ Nomenclatura descritiva
# ✅ Testes obrigatórios incluídos
# ✅ Limpeza automática de arquivos temporários
# ===============================================================================

# ==== FUNÇÕES AUXILIARES ====
show_progress() {
  local current=$1
  local total=$2
  local operation=$3
  local percent=$((current * 100 / total))
  local bar_length=30
  local filled_length=$((percent * bar_length / 100))

  printf "\r  → $operation: ["
  printf "%*s" $filled_length | tr ' ' '█'
  printf "%*s" $((bar_length - filled_length)) | tr ' ' '░'
  printf "] %d%% (%d/%d)" $percent $current $total
}

# Função para mostrar spinner durante operações longas
show_spinner() {
  local pid=$1
  local operation=$2
  local spin='⠋⠙⠹⠸⠼⠴⠦⠧⠇⠏'
  local i=0

  echo -n "  → $operation: "
  while kill -0 $pid 2>/dev/null; do
    i=$(( (i+1) %10 ))
    printf "\r  → $operation: ${spin:$i:1}"
    sleep 0.1
  done
  printf "\r  → $operation: ✅ Concluído\n"
}

# Função para monitorar tamanho de arquivo
monitor_file_size() {
  local file=$1
  local operation=$2

  echo "  → $operation..."
  while [ ! -f "$file" ]; do
    sleep 1
  done

  local start_time=$(date +%s)
  while true; do
    if [ -f "$file" ]; then
      local size=$(du -h "$file" 2>/dev/null | cut -f1)
      local elapsed=$(($(date +%s) - start_time))
      printf "\r  → $operation: %s (tempo: %ds)" "$size" "$elapsed"
      sleep 2
    else
      break
    fi
  done
  echo ""
}

# ==== VALIDAÇÃO DE ARGUMENTOS ====
if [ -z "$1" ]; then
  echo "❌ ERRO: Informe o CTID do container."
  echo ""
  echo "📖 USO CORRETO:"
  echo "   ./gerar-template-lxc.sh <CTID>"
  echo ""
  echo "📝 EXEMPLO:"
  echo "   ./gerar-template-lxc.sh 104"
  echo ""
  echo "📋 O QUE ESTE SCRIPT FAZ:"
  echo "   ✅ Cria backup do container especificado"
  echo "   ✅ Remove configurações específicas (rede, SSH, logs)"
  echo "   ✅ Preserva AzerothCore e aplicações"
  echo "   ✅ Gera template limpo para reutilização"
  echo "   ✅ Remove backup automaticamente (economiza espaço)"
  echo ""
  exit 1
fi

# Validar se CTID é numérico
if ! [[ "$1" =~ ^[0-9]+$ ]]; then
  echo "❌ ERRO: CTID deve ser um número válido"
  echo "   Recebido: '$1'"
  echo "   Esperado: número (ex: 104)"
  exit 1
fi

CTID=$1
NOME_TEMPLATE="template-azerothcore-ct$CTID"
DUMP_DIR="/var/lib/vz/dump"
CACHE_DIR="/var/lib/vz/template/cache"
TMPDIR="/tmp/template-$CTID"

echo "==============================================================================="
echo "🎮 GERADOR DE TEMPLATE AZEROTHCORE - INICIANDO"
echo "==============================================================================="
echo "📦 Container ID: $CTID"
echo "🏷️  Template: $NOME_TEMPLATE.tar.gz"
echo "📅 Data/Hora: $(date '+%Y-%m-%d %H:%M:%S')"
echo "==============================================================================="

# ==== VERIFICAÇÕES INICIAIS ====
echo "🔍 Verificando se o container existe..."
if ! pct list | grep -q "^$CTID "; then
  echo "❌ ERRO: Container $CTID não encontrado"
  exit 1
fi

echo "💾 Verificando espaço em disco..."
# Verificar espaço em /var/lib/vz (precisa de pelo menos 5GB livres)
SPACE_VZ=$(df /var/lib/vz | awk 'NR==2 {print $4}')
if [ "$SPACE_VZ" -lt 5242880 ]; then  # 5GB em KB
  echo "❌ ERRO: Espaço insuficiente em /var/lib/vz (mínimo 5GB livres)"
  echo "   Espaço disponível: $(($SPACE_VZ / 1024 / 1024))GB"
  exit 1
fi

# Verificar espaço em /tmp (precisa de pelo menos 3GB livres)
SPACE_TMP=$(df /tmp | awk 'NR==2 {print $4}')
if [ "$SPACE_TMP" -lt 3145728 ]; then  # 3GB em KB
  echo "❌ ERRO: Espaço insuficiente em /tmp (mínimo 3GB livres)"
  echo "   Espaço disponível: $(($SPACE_TMP / 1024 / 1024))GB"
  exit 1
fi

echo "✅ Verificações iniciais concluídas"

# ==== BACKUP ====
echo "⏳ Gerando backup com ZSTD (modo 'stop')..."
echo "📊 Monitorando progresso do backup:"

# Executar vzdump em background e monitorar
vzdump $CTID --compress zstd --mode stop --storage local --remove 0 --node $(hostname) &
BACKUP_PID=$!

# Monitorar o progresso do backup
echo "  → Iniciando backup do container $CTID..."
while kill -0 $BACKUP_PID 2>/dev/null; do
  # Procurar pelo arquivo de backup sendo criado
  CURRENT_BACKUP=$(ls -t $DUMP_DIR/vzdump-lxc-${CTID}-*.tar.zst 2>/dev/null | head -n1)
  if [ -f "$CURRENT_BACKUP" ]; then
    SIZE=$(du -h "$CURRENT_BACKUP" 2>/dev/null | cut -f1)
    printf "\r  → Backup em progresso: %s" "$SIZE"
  else
    printf "\r  → Preparando backup..."
  fi
  sleep 2
done

# Aguardar o processo terminar
wait $BACKUP_PID
BACKUP_EXIT_CODE=$?

if [ $BACKUP_EXIT_CODE -eq 0 ]; then
  echo ""
  echo "✅ Backup concluído com sucesso"
else
  echo ""
  echo "❌ ERRO: Backup falhou com código $BACKUP_EXIT_CODE"
  exit 2
fi

# ==== LOCALIZAR BACKUP ====
BACKUP_FILE=$(ls -t $DUMP_DIR/vzdump-lxc-${CTID}-*.tar.zst 2>/dev/null | head -n1)

if [ ! -f "$BACKUP_FILE" ]; then
  echo "❌ ERRO: Backup não encontrado após criação. Abortando."
  exit 2
fi

echo "✅ Backup gerado: $BACKUP_FILE"

# ==== EXTRAÇÃO ====
echo "📂 Extraindo backup para diretório temporário..."
echo "📊 Monitorando extração:"
rm -rf "$TMPDIR"
mkdir -p "$TMPDIR"
cd "$TMPDIR"

# Obter tamanho do arquivo para calcular progresso
BACKUP_SIZE=$(stat -c%s "$BACKUP_FILE")
echo "  → Arquivo: $(basename $BACKUP_FILE) ($(du -h "$BACKUP_FILE" | cut -f1))"

# Extrair com monitoramento
echo "  → Iniciando extração..."
tar -I zstd -xf "$BACKUP_FILE" &
EXTRACT_PID=$!

# Monitorar progresso da extração
START_TIME=$(date +%s)
while kill -0 $EXTRACT_PID 2>/dev/null; do
  ELAPSED=$(($(date +%s) - START_TIME))
  if [ -d "$TMPDIR" ]; then
    EXTRACTED_SIZE=$(du -s "$TMPDIR" 2>/dev/null | cut -f1)
    printf "\r  → Extraindo: %ds elapsed, %dKB extraídos" "$ELAPSED" "$EXTRACTED_SIZE"
  fi
  sleep 1
done

wait $EXTRACT_PID
echo ""
echo "✅ Extração concluída"

# ==== MONTAGEM ROOTFS ====
if [ -f root.pxar ]; then
  echo "📂 Extraindo root.pxar..."
  echo "📊 Progresso da extração do sistema de arquivos:"
  mkdir rootfs

  # Mostrar progresso do pxar
  echo "  → Iniciando extração do sistema de arquivos..."
  pxar -xf root.pxar -C rootfs --verbose | while read line; do
    echo -ne "\r  → Extraindo: $(echo $line | tail -c 50)"
  done
  echo ""
  echo "✅ Sistema de arquivos extraído"

elif [ -d rootfs ]; then
  echo "📂 rootfs já está em formato de diretório"
else
  echo "❌ ERRO: rootfs não detectado após extração"
  exit 3
fi

# ==== LIMPEZA DE CONFIGURAÇÕES DO TEMPLATE ====
echo "🧹 Limpando configurações de rede e sistema para template..."

# Limpar configurações de rede
echo "  → Limpando configurações de rede..."
rm -f rootfs/etc/systemd/network/*
rm -f rootfs/etc/netplan/*
rm -f rootfs/etc/network/interfaces.d/*
echo "" > rootfs/etc/network/interfaces
echo "auto lo" >> rootfs/etc/network/interfaces
echo "iface lo inet loopback" >> rootfs/etc/network/interfaces

# Limpar hostname
echo "  → Resetando hostname..."
echo "azerothcore-template" > rootfs/etc/hostname
sed -i '/*********/d' rootfs/etc/hosts

# Limpar SSH host keys (serão regenerados no primeiro boot)
echo "  → Removendo SSH host keys..."
rm -f rootfs/etc/ssh/ssh_host_*

# Limpar logs
echo "  → Limpando logs do sistema..."
find rootfs/var/log -type f -name "*.log" -exec truncate -s 0 {} \;
find rootfs/var/log -type f -name "*.log.*" -delete
rm -rf rootfs/var/log/journal/*
rm -rf rootfs/tmp/*
rm -rf rootfs/var/tmp/*

# Limpar cache de pacotes
echo "  → Limpando cache de pacotes..."
rm -rf rootfs/var/cache/apt/archives/*.deb
rm -rf rootfs/var/lib/apt/lists/*

# Limpar histórico de comandos
echo "  → Limpando histórico..."
rm -f rootfs/root/.bash_history
rm -f rootfs/home/<USER>/.bash_history 2>/dev/null

# Limpar machine-id (será regenerado)
echo "  → Resetando machine-id..."
echo "" > rootfs/etc/machine-id
echo "" > rootfs/var/lib/dbus/machine-id

echo "✅ Limpeza de configurações concluída"

# ==== CRIAÇÃO DO TEMPLATE ====
echo "🛠️ Gerando template .tar.gz para Proxmox..."
echo "📊 Monitorando compressão:"
cd rootfs

# Contar arquivos e calcular tamanho
TOTAL_FILES=$(find . -type f | wc -l)
TOTAL_SIZE=$(du -s . | cut -f1)
echo "  → Total: $TOTAL_FILES arquivos ($(du -sh . | cut -f1))"
echo "  → Iniciando compressão..."

# Criar template com monitoramento
tar --numeric-owner -czf "$CACHE_DIR/$NOME_TEMPLATE.tar.gz" . &
TAR_PID=$!

# Monitorar progresso da compressão
START_TIME=$(date +%s)
while kill -0 $TAR_PID 2>/dev/null; do
  ELAPSED=$(($(date +%s) - START_TIME))
  if [ -f "$CACHE_DIR/$NOME_TEMPLATE.tar.gz" ]; then
    COMPRESSED_SIZE=$(du -h "$CACHE_DIR/$NOME_TEMPLATE.tar.gz" 2>/dev/null | cut -f1)
    printf "\r  → Comprimindo: %s (%ds)" "$COMPRESSED_SIZE" "$ELAPSED"
  else
    printf "\r  → Preparando compressão... (%ds)" "$ELAPSED"
  fi
  sleep 1
done

wait $TAR_PID
echo ""
echo "✅ Template comprimido com sucesso"

# ==== VERIFICAÇÃO FINAL ====
if [ -f "$CACHE_DIR/$NOME_TEMPLATE.tar.gz" ]; then
  TEMPLATE_SIZE=$(du -h "$CACHE_DIR/$NOME_TEMPLATE.tar.gz" | cut -f1)
  echo "✅ Template criado com sucesso:"
  echo "    📁 Arquivo: $CACHE_DIR/$NOME_TEMPLATE.tar.gz"
  echo "    📏 Tamanho: $TEMPLATE_SIZE"
else
  echo "❌ ERRO: Template não foi criado corretamente"
  exit 4
fi

# ==== LIMPEZA FINAL ====
echo "🧹 Realizando limpeza final..."

# Remover diretório temporário
cd /
rm -rf "$TMPDIR"

# Remover o backup original para economizar espaço
echo "🗑️ Removendo backup original para economizar espaço..."
if [ -f "$BACKUP_FILE" ]; then
  rm -f "$BACKUP_FILE"
  echo "✅ Backup removido: $(basename $BACKUP_FILE)"
fi

echo "🧹 Limpeza finalizada."
echo ""
echo "🎉 TEMPLATE CRIADO COM SUCESSO!"
echo "📋 Resumo:"
echo "   • Template: $NOME_TEMPLATE.tar.gz"
echo "   • Tamanho: $TEMPLATE_SIZE"
echo "   • Localização: $CACHE_DIR/"
echo ""
echo "💡 Para usar o template:"
echo "   1. Crie um novo container no Proxmox"
echo "   2. Selecione o template '$NOME_TEMPLATE.tar.gz'"
echo "   3. Configure rede, CPU, RAM conforme necessário"
echo "   4. O AzerothCore estará pronto para uso!"

exit 0
