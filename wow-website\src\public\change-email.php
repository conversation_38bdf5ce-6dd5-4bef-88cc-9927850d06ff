<?php
/**
 * Página de Alteração de Email
 * 
 * <AUTHOR> Agent
 * @version 1.0
 */

require_once '../includes/config/config.php';

// Verificar se está logado
if (!isLoggedIn()) {
    redirect('/login?redirect=' . urlencode('/change-email'));
}

$errors = [];
$success = false;

// Obter email atual
$current_email = '';
try {
    $auth_db = DatabaseManager::getConnection('auth');
    $stmt = $auth_db->prepare("SELECT email FROM account WHERE id = ?");
    $stmt->execute([$_SESSION['user_id']]);
    $account = $stmt->fetch();
    $current_email = $account['email'] ?? '';
} catch (Exception $e) {
    $errors[] = 'Erro ao carregar dados da conta.';
}

// Processar formulário
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Verificar CSRF
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $errors[] = 'Token de segurança inválido.';
    } else {
        $new_email = sanitizeInput($_POST['new_email'] ?? '', 'email');
        $password = $_POST['password'] ?? '';
        
        // Validações
        if (empty($new_email)) {
            $errors[] = 'Novo email é obrigatório.';
        } elseif (!filter_var($new_email, FILTER_VALIDATE_EMAIL)) {
            $errors[] = 'Email inválido.';
        }
        
        if (empty($password)) {
            $errors[] = 'Senha é obrigatória para confirmar a alteração.';
        }
        
        if ($new_email === $current_email) {
            $errors[] = 'O novo email deve ser diferente do atual.';
        }
        
        // Verificar se email já está em uso
        if (empty($errors)) {
            try {
                $auth_db = DatabaseManager::getConnection('auth');
                $stmt = $auth_db->prepare("SELECT id FROM account WHERE email = ? AND id != ?");
                $stmt->execute([$new_email, $_SESSION['user_id']]);
                if ($stmt->fetch()) {
                    $errors[] = 'Este email já está sendo usado por outra conta.';
                }
            } catch (Exception $e) {
                $errors[] = 'Erro ao verificar email.';
            }
        }
        
        // Verificar senha
        if (empty($errors)) {
            try {
                $auth_db = DatabaseManager::getConnection('auth');
                $stmt = $auth_db->prepare("SELECT sha_pass_hash FROM account WHERE id = ?");
                $stmt->execute([$_SESSION['user_id']]);
                $account = $stmt->fetch();
                
                if ($account) {
                    $password_hash = strtoupper(sha1(strtoupper($_SESSION['username']) . ':' . strtoupper($password)));
                    
                    if (!hash_equals($account['sha_pass_hash'], $password_hash)) {
                        $errors[] = 'Senha incorreta.';
                    }
                } else {
                    $errors[] = 'Conta não encontrada.';
                }
                
            } catch (Exception $e) {
                $errors[] = 'Erro ao verificar senha.';
                writeLog('ERROR', 'Change email verification error: ' . $e->getMessage());
            }
        }
        
        // Alterar email se não há erros
        if (empty($errors)) {
            try {
                $auth_db = DatabaseManager::getConnection('auth');
                $stmt = $auth_db->prepare("UPDATE account SET email = ? WHERE id = ?");
                $stmt->execute([$new_email, $_SESSION['user_id']]);
                
                // Atualizar sessão
                $_SESSION['email'] = $new_email;
                
                $success = true;
                writeLog('INFO', 'Email changed', [
                    'username' => $_SESSION['username'],
                    'old_email' => $current_email,
                    'new_email' => $new_email,
                    'ip' => getRealIP()
                ]);
                
                $current_email = $new_email;
                
            } catch (Exception $e) {
                $errors[] = 'Erro ao alterar email. Tente novamente.';
                writeLog('ERROR', 'Change email error: ' . $e->getMessage());
            }
        }
    }
}

// Dados para o template
$page_data = [
    'title' => 'Alterar Email - ' . SITE_NAME,
    'description' => 'Alterar email da conta',
    'current_page' => 'change-email'
];

?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= escape($page_data['title']) ?></title>
    <meta name="description" content="<?= escape($page_data['description']) ?>">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="<?= ASSETS_PATH ?>/css/style.css" rel="stylesheet">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?= ASSETS_PATH ?>/images/favicon.ico">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark fixed-top">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-shield-alt me-2"></i>
                <?= SITE_NAME ?>
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">Início</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/download">Download</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/ranking">Ranking</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/forum">Fórum</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/donate">Doações</a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user"></i> <?= escape($_SESSION['username']) ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/account">Minha Conta</a></li>
                            <li><a class="dropdown-item" href="/characters">Personagens</a></li>
                            <?php if (isAdmin()): ?>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="/admin">Painel Admin</a></li>
                            <?php endif; ?>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="/logout">Sair</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container mt-5 pt-5">
        <div class="row justify-content-center">
            <div class="col-md-6 col-lg-5">
                <div class="card shadow">
                    <div class="card-header bg-info text-white text-center">
                        <h4 class="mb-0">
                            <i class="fas fa-envelope"></i> Alterar Email
                        </h4>
                    </div>
                    <div class="card-body">
                        <?php if ($success): ?>
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle"></i>
                                <strong>Email alterado com sucesso!</strong><br>
                                Seu email foi atualizado para: <strong><?= escape($current_email) ?></strong>
                            </div>
                            <div class="text-center">
                                <a href="/account" class="btn btn-primary">
                                    <i class="fas fa-user"></i> Voltar à Conta
                                </a>
                            </div>
                        <?php else: ?>
                            <div class="mb-3">
                                <label class="form-label text-muted">Email Atual</label>
                                <div class="form-control-plaintext bg-light p-2 rounded">
                                    <?= escape($current_email) ?>
                                </div>
                            </div>
                            
                            <?php if (!empty($errors)): ?>
                                <div class="alert alert-danger">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    <strong>Erro(s) encontrado(s):</strong>
                                    <ul class="mb-0 mt-2">
                                        <?php foreach ($errors as $error): ?>
                                            <li><?= escape($error) ?></li>
                                        <?php endforeach; ?>
                                    </ul>
                                </div>
                            <?php endif; ?>
                            
                            <form method="POST" action="/change-email">
                                <input type="hidden" name="csrf_token" value="<?= generateCSRFToken() ?>">
                                
                                <div class="mb-3">
                                    <label for="new_email" class="form-label">Novo Email</label>
                                    <input type="email" class="form-control" id="new_email" name="new_email" 
                                           value="<?= escape($_POST['new_email'] ?? '') ?>" required>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="password" class="form-label">Senha (para confirmar)</label>
                                    <input type="password" class="form-control" id="password" name="password" required>
                                    <div class="form-text">Digite sua senha para confirmar a alteração</div>
                                </div>
                                
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-info">
                                        <i class="fas fa-envelope"></i> Alterar Email
                                    </button>
                                </div>
                            </form>
                        <?php endif; ?>
                        
                        <hr>
                        <div class="text-center">
                            <a href="/account" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left"></i> Voltar à Conta
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
