# ⚙️ Configurações Avançadas - AzerothCore

## 📋 Índice
1. [Configurações de Performance](#configurações-de-performance)
2. [Se<PERSON>rança Avançada](#segurança-avançada)
3. [Otimizações de Rede](#otimizações-de-rede)
4. [Configurações do AzerothCore](#configurações-do-azerothcore)
5. [Tuning do MySQL](#tuning-do-mysql)

---

## ⚡ Configurações de Performance

### 1. **Kernel Parameters** (`/etc/sysctl.conf`)
```bash
# Network Performance
net.core.rmem_default = 262144
net.core.rmem_max = 16777216
net.core.wmem_default = 262144
net.core.wmem_max = 16777216
net.ipv4.tcp_rmem = 4096 87380 16777216
net.ipv4.tcp_wmem = 4096 65536 16777216
net.core.netdev_max_backlog = 5000
net.ipv4.tcp_window_scaling = 1
net.ipv4.tcp_timestamps = 1
net.ipv4.tcp_sack = 1
net.ipv4.tcp_no_metrics_save = 1
net.core.netdev_budget = 600

# Memory Management
vm.swappiness = 10
vm.dirty_ratio = 15
vm.dirty_background_ratio = 5
vm.overcommit_memory = 1
vm.min_free_kbytes = 65536

# File System
fs.file-max = 2097152
fs.nr_open = 1048576

# Security
kernel.dmesg_restrict = 1
kernel.kptr_restrict = 2
net.ipv4.conf.all.send_redirects = 0
net.ipv4.conf.default.send_redirects = 0
net.ipv4.conf.all.accept_redirects = 0
net.ipv4.conf.default.accept_redirects = 0
```

### 2. **Limits Configuration** (`/etc/security/limits.conf`)
```bash
# AzerothCore user limits
azerothcore soft nofile 65536
azerothcore hard nofile 65536
azerothcore soft nproc 32768
azerothcore hard nproc 32768

# MySQL user limits
mysql soft nofile 65536
mysql hard nofile 65536
mysql soft nproc 32768
mysql hard nproc 32768

# Root limits
root soft nofile 65536
root hard nofile 65536
```

### 3. **CPU Governor** (`/etc/default/cpufrequtils`)
```bash
# Set CPU governor to performance for gaming server
GOVERNOR="performance"
MIN_SPEED="0"
MAX_SPEED="0"
```

### 4. **I/O Scheduler Optimization**
```bash
# Script para otimizar I/O scheduler
#!/bin/bash
# /opt/scripts/optimize-io.sh

# Para SSDs - usar noop ou deadline
for disk in /sys/block/sd*; do
    if [ -f "$disk/queue/scheduler" ]; then
        echo "deadline" > "$disk/queue/scheduler"
        echo "Scheduler para $(basename $disk): deadline"
    fi
done

# Otimizar read-ahead
for disk in /sys/block/sd*; do
    if [ -f "$disk/queue/read_ahead_kb" ]; then
        echo "128" > "$disk/queue/read_ahead_kb"
        echo "Read-ahead para $(basename $disk): 128KB"
    fi
done
```

---

## 🛡️ Segurança Avançada

### 1. **Firewall Avançado** (`/opt/scripts/setup-firewall.sh`)
```bash
#!/bin/bash
# setup-firewall.sh - Configuração avançada de firewall

# Reset UFW
ufw --force reset

# Políticas padrão
ufw default deny incoming
ufw default allow outgoing

# SSH - apenas IPs específicos
ufw allow from ***********/24 to any port 22
ufw allow from SEU_IP_CASA to any port 22

# AzerothCore - com rate limiting
ufw limit 3724/tcp  # AuthServer com rate limiting
ufw allow 8085/tcp  # WorldServer

# MySQL - apenas local
ufw deny 3306/tcp

# HTTP/HTTPS para site (se houver)
ufw allow 80/tcp
ufw allow 443/tcp

# Logs
ufw logging on

# Ativar
ufw --force enable

echo "✅ Firewall configurado com sucesso!"
```

### 2. **Fail2Ban Avançado** (`/etc/fail2ban/jail.local`)
```ini
[DEFAULT]
bantime = 3600
findtime = 600
maxretry = 3
backend = systemd
usedns = warn
logencoding = auto
enabled = false
mode = normal
filter = %(__name__)s[mode=%(mode)s]

[sshd]
enabled = true
port = ssh
logpath = %(sshd_log)s
backend = %(sshd_backend)s
maxretry = 3
bantime = 1800

[azerothcore-auth]
enabled = true
port = 3724
filter = azerothcore-auth
logpath = /opt/azerothcore/logs/Auth.log
maxretry = 5
bantime = 3600
findtime = 300

[azerothcore-bruteforce]
enabled = true
port = 3724,8085
filter = azerothcore-bruteforce
logpath = /opt/azerothcore/logs/*.log
maxretry = 10
bantime = 7200
findtime = 600

[mysql-auth]
enabled = true
port = 3306
filter = mysql-auth
logpath = /var/log/mysql/error.log
maxretry = 3
bantime = 3600
```

### 3. **Filtros Fail2Ban** (`/etc/fail2ban/filter.d/azerothcore-auth.conf`)
```ini
[Definition]
failregex = ^.*\[AUTH\] IP: <HOST> failed to authenticate.*$
            ^.*\[AUTH\] Account .* (IP: <HOST>) failed to authenticate.*$
            ^.*\[AUTH\] Wrong password for account .* (IP: <HOST>).*$

ignoreregex =

[Init]
datepattern = ^%%Y-%%m-%%d %%H:%%M:%%S
```

### 4. **Monitoramento de Intrusão** (`/opt/scripts/intrusion-detection.sh`)
```bash
#!/bin/bash
# intrusion-detection.sh - Detecção básica de intrusão

ALERT_EMAIL="<EMAIL>"
LOG_FILE="/var/log/intrusion-detection.log"

check_suspicious_activity() {
    # Verificar logins suspeitos
    local suspicious_ips=$(last -i | grep -E "(unknown|invalid)" | awk '{print $3}' | sort | uniq -c | sort -nr | head -5)
    
    if [ ! -z "$suspicious_ips" ]; then
        echo "[$(date)] IPs suspeitos detectados: $suspicious_ips" >> $LOG_FILE
    fi
    
    # Verificar processos suspeitos
    local suspicious_procs=$(ps aux | grep -E "(nc|netcat|nmap|masscan)" | grep -v grep)
    
    if [ ! -z "$suspicious_procs" ]; then
        echo "[$(date)] Processos suspeitos: $suspicious_procs" >> $LOG_FILE
    fi
    
    # Verificar conexões de rede suspeitas
    local suspicious_conns=$(netstat -an | grep ESTABLISHED | awk '{print $5}' | cut -d: -f1 | sort | uniq -c | sort -nr | head -10)
    
    # Verificar modificações em arquivos críticos
    find /etc/passwd /etc/shadow /etc/sudoers -newer /tmp/last_check 2>/dev/null
    if [ $? -eq 0 ]; then
        echo "[$(date)] Arquivos críticos modificados!" >> $LOG_FILE
    fi
    
    touch /tmp/last_check
}

# Executar verificações
check_suspicious_activity
```

---

## 🌐 Otimizações de Rede

### 1. **TCP Tuning** (`/etc/sysctl.d/99-network-performance.conf`)
```bash
# TCP Performance Tuning
net.ipv4.tcp_congestion_control = bbr
net.core.default_qdisc = fq

# TCP Buffer Sizes
net.ipv4.tcp_rmem = 4096 87380 16777216
net.ipv4.tcp_wmem = 4096 65536 16777216
net.core.rmem_max = 16777216
net.core.wmem_max = 16777216

# TCP Options
net.ipv4.tcp_window_scaling = 1
net.ipv4.tcp_timestamps = 1
net.ipv4.tcp_sack = 1
net.ipv4.tcp_fack = 1
net.ipv4.tcp_low_latency = 1

# Connection Tracking
net.netfilter.nf_conntrack_max = 1048576
net.netfilter.nf_conntrack_tcp_timeout_established = 7200

# Network Security
net.ipv4.conf.all.rp_filter = 1
net.ipv4.conf.default.rp_filter = 1
net.ipv4.icmp_echo_ignore_broadcasts = 1
net.ipv4.icmp_ignore_bogus_error_responses = 1
net.ipv4.conf.all.log_martians = 1
net.ipv4.conf.default.log_martians = 1
```

### 2. **Quality of Service (QoS)** (`/opt/scripts/setup-qos.sh`)
```bash
#!/bin/bash
# setup-qos.sh - Configuração de QoS para priorizar tráfego do jogo

INTERFACE="eth0"  # Ajustar conforme sua interface

# Limpar regras existentes
tc qdisc del dev $INTERFACE root 2>/dev/null

# Criar qdisc principal
tc qdisc add dev $INTERFACE root handle 1: htb default 30

# Classes principais
tc class add dev $INTERFACE parent 1: classid 1:1 htb rate 1000mbit
tc class add dev $INTERFACE parent 1:1 classid 1:10 htb rate 800mbit ceil 1000mbit  # Gaming
tc class add dev $INTERFACE parent 1:1 classid 1:20 htb rate 150mbit ceil 300mbit   # Web
tc class add dev $INTERFACE parent 1:1 classid 1:30 htb rate 50mbit ceil 200mbit    # Outros

# Filtros para tráfego do jogo
tc filter add dev $INTERFACE protocol ip parent 1:0 prio 1 u32 match ip dport 3724 0xffff flowid 1:10
tc filter add dev $INTERFACE protocol ip parent 1:0 prio 1 u32 match ip dport 8085 0xffff flowid 1:10
tc filter add dev $INTERFACE protocol ip parent 1:0 prio 1 u32 match ip sport 3724 0xffff flowid 1:10
tc filter add dev $INTERFACE protocol ip parent 1:0 prio 1 u32 match ip sport 8085 0xffff flowid 1:10

# Filtros para HTTP/HTTPS
tc filter add dev $INTERFACE protocol ip parent 1:0 prio 2 u32 match ip dport 80 0xffff flowid 1:20
tc filter add dev $INTERFACE protocol ip parent 1:0 prio 2 u32 match ip dport 443 0xffff flowid 1:20

echo "✅ QoS configurado para priorizar tráfego do jogo"
```

---

## 🎮 Configurações do AzerothCore

### 1. **worldserver.conf Otimizado**
```ini
###################################################################################################
# AZEROTHCORE WORLDSERVER CONFIGURATION - OTIMIZADO PARA PERFORMANCE
###################################################################################################

#
# PERFORMANCE SETTINGS
#
PlayerLimit = 100
MaxPingTime = 30000
SessionAddDelay = 10000
WorldServerPort = 8085
BindIP = "0.0.0.0"

#
# DATABASE SETTINGS
#
LoginDatabaseInfo = "127.0.0.1;3306;acore;SUA_SENHA;auth"
WorldDatabaseInfo = "127.0.0.1;3306;acore;SUA_SENHA;world"
CharacterDatabaseInfo = "127.0.0.1;3306;acore;SUA_SENHA;characters"

# Connection Pooling
LoginDatabase.WorkerThreads = 2
WorldDatabase.WorkerThreads = 4
CharacterDatabase.WorkerThreads = 2

#
# LOGGING
#
LogsDir = "/opt/azerothcore/logs"
LogLevel = 2
LogFile = "Server.log"
LogTimestamp = 1
LogFileLevel = 2
LogColors = 1

# Specific Logs
LogDB.Opt.Enabled = 1
LogDB.Opt.File = "DBErrors.log"

#
# PERFORMANCE RATES
#
Rate.XP.Kill = 1
Rate.XP.Quest = 1
Rate.XP.Explore = 1
Rate.Drop.Item.Poor = 1
Rate.Drop.Item.Normal = 1
Rate.Drop.Item.Uncommon = 1
Rate.Drop.Item.Rare = 1
Rate.Drop.Item.Epic = 1
Rate.Drop.Item.Legendary = 1
Rate.Drop.Item.Artifact = 1
Rate.Drop.Money = 1

#
# ANTI-CHEAT
#
Warden.Enabled = 1
AntiCheat.Enable = 1
AntiCheat.ReportsForIngameWarnings = 50
AntiCheat.ReportsForIngameBan = 70

#
# SECURITY
#
PacketLogFile = ""
GMLogFile = "GM.log"
CharLogFile = "Char.log"
ChatLogFile = "Chat.log"

# Account Security
Account.PasswordSecurity = 1
Account.PasswordMinLength = 8

#
# WORLD SETTINGS
#
GameType = 1
RealmZone = 1
Expansion = 2
StrictPlayerNames = 2
StrictCharterNames = 2
StrictPetNames = 2

#
# PERFORMANCE OPTIMIZATIONS
#
MapUpdate.Threads = 4
vmap.enableLOS = 1
vmap.enableHeight = 1
vmap.petLOS = 1

# Grid Settings
GridUnload = 1
GridCleanUpDelay = 300000
MapUpdateInterval = 100

# Visibility
Visibility.GroupMode = 0
Visibility.Distance.Continents = 90
Visibility.Distance.Instances = 120
Visibility.Distance.BG = 180

#
# CHAT SETTINGS
#
ChatFakeMessagePreventing = 1
ChatStrictLinkChecking.Severity = 0
ChatStrictLinkChecking.Kick = 0

#
# GUILD SETTINGS
#
Guild.AllowMultipleGuildMaster = 0

#
# BATTLEGROUND SETTINGS
#
Battleground.CastDeserter = 1
Battleground.QueueAnnouncer.Enable = 1

#
# DUNGEON FINDER
#
DungeonFinder.Enable = 1

#
# WINTERGRASP
#
Wintergrasp.Enable = 1
Wintergrasp.PlayerMax = 100
Wintergrasp.PlayerMin = 0
Wintergrasp.PlayerMinLvl = 77
Wintergrasp.BattleTimer = 1800000
Wintergrasp.NoBattleTimer = 900000
Wintergrasp.RestartAfterCrash = 3600000
```

### 2. **authserver.conf Otimizado**
```ini
###################################################################################################
# AZEROTHCORE AUTHSERVER CONFIGURATION
###################################################################################################

#
# NETWORK SETTINGS
#
RealmServerPort = 3724
BindIP = "0.0.0.0"

#
# DATABASE
#
LoginDatabaseInfo = "127.0.0.1;3306;acore;SUA_SENHA;auth"

#
# LOGGING
#
LogsDir = "/opt/azerothcore/logs"
LogLevel = 2
LogFile = "Auth.log"
LogTimestamp = 1
LogFileLevel = 2
LogColors = 1

#
# SECURITY
#
UseProcessors = 0
ProcessPriority = 1
WrongPass.MaxCount = 3
WrongPass.BanTime = 600
WrongPass.BanType = 0
WrongPass.Logging = 1

#
# PERFORMANCE
#
MaxPingTime = 30
LoginDatabase.WorkerThreads = 2
LoginDatabase.SynchThreads = 1
```

---

## 🗄️ Tuning do MySQL

### 1. **my.cnf Otimizado** (`/etc/mysql/mysql.conf.d/mysqld.cnf`)
```ini
[mysqld]
# Basic Settings
user = mysql
pid-file = /var/run/mysqld/mysqld.pid
socket = /var/run/mysqld/mysqld.sock
port = 3306
basedir = /usr
datadir = /var/lib/mysql
tmpdir = /tmp
lc-messages-dir = /usr/share/mysql
skip-external-locking

# Network Settings
bind-address = 127.0.0.1
max_connections = 500
max_connect_errors = 1000000
max_allowed_packet = 256M
interactive_timeout = 28800
wait_timeout = 28800
connect_timeout = 60

# InnoDB Settings
default_storage_engine = InnoDB
innodb_buffer_pool_size = 6G  # 70-80% da RAM disponível
innodb_buffer_pool_instances = 6
innodb_log_file_size = 1G
innodb_log_buffer_size = 64M
innodb_flush_log_at_trx_commit = 2
innodb_flush_method = O_DIRECT
innodb_file_per_table = 1
innodb_open_files = 400
innodb_io_capacity = 400
innodb_io_capacity_max = 2000
innodb_read_io_threads = 8
innodb_write_io_threads = 8
innodb_thread_concurrency = 0
innodb_lock_wait_timeout = 120

# Query Cache
query_cache_type = 1
query_cache_size = 512M
query_cache_limit = 4M
query_cache_min_res_unit = 2k

# MyISAM Settings
key_buffer_size = 256M
myisam_sort_buffer_size = 128M
myisam_max_sort_file_size = 2G
myisam_repair_threads = 1

# Thread Settings
thread_cache_size = 50
thread_stack = 256K

# Table Settings
table_open_cache = 4000
table_definition_cache = 2000

# Sort/Group Settings
sort_buffer_size = 4M
read_buffer_size = 2M
read_rnd_buffer_size = 8M
join_buffer_size = 4M
group_concat_max_len = 1024

# Temporary Tables
tmp_table_size = 256M
max_heap_table_size = 256M

# Binary Logging
log_bin = /var/log/mysql/mysql-bin.log
binlog_format = ROW
expire_logs_days = 7
max_binlog_size = 100M
sync_binlog = 0

# Error Logging
log_error = /var/log/mysql/error.log

# Slow Query Log
slow_query_log = 1
slow_query_log_file = /var/log/mysql/slow.log
long_query_time = 2
log_queries_not_using_indexes = 1
log_slow_admin_statements = 1

# General Log (desabilitado para performance)
general_log = 0

# Performance Schema
performance_schema = ON
performance_schema_max_table_instances = 400
performance_schema_max_table_handles = 4000

[mysql]
default-character-set = utf8mb4

[mysqldump]
quick
quote-names
max_allowed_packet = 256M

[mysql_safe]
log-error = /var/log/mysql/error.log
```

### 2. **Script de Otimização MySQL** (`/opt/scripts/optimize-mysql.sh`)
```bash
#!/bin/bash
# optimize-mysql.sh - Otimização automática do MySQL

MYSQL_USER="root"
MYSQL_PASS="SUA_SENHA_ROOT"

echo "🔧 Iniciando otimização do MySQL..."

# 1. Analisar e otimizar tabelas
mysql -u $MYSQL_USER -p$MYSQL_PASS << EOF
-- Otimizar tabelas principais
USE auth;
OPTIMIZE TABLE account, account_access, account_banned, realmlist;
ANALYZE TABLE account, account_access, account_banned, realmlist;

USE characters;
OPTIMIZE TABLE characters, character_inventory, character_spell, character_achievement;
ANALYZE TABLE characters, character_inventory, character_spell, character_achievement;

USE world;
OPTIMIZE TABLE creature, creature_template, gameobject, gameobject_template;
OPTIMIZE TABLE quest_template, item_template, spell_dbc, achievement_dbc;
ANALYZE TABLE creature, creature_template, gameobject, gameobject_template;
ANALYZE TABLE quest_template, item_template, spell_dbc, achievement_dbc;

-- Verificar fragmentação
SELECT 
    table_schema,
    table_name,
    ROUND(data_length/1024/1024,2) AS data_mb,
    ROUND(index_length/1024/1024,2) AS index_mb,
    ROUND((data_length+index_length)/1024/1024,2) AS total_mb,
    ROUND(data_free/1024/1024,2) AS free_mb
FROM information_schema.tables 
WHERE table_schema IN ('auth','characters','world') 
    AND data_free > 0
ORDER BY free_mb DESC;

-- Estatísticas de performance
SHOW ENGINE INNODB STATUS\G
EOF

# 2. Flush logs
mysql -u $MYSQL_USER -p$MYSQL_PASS -e "FLUSH LOGS;"

# 3. Limpar binary logs antigos
mysql -u $MYSQL_USER -p$MYSQL_PASS -e "PURGE BINARY LOGS BEFORE DATE_SUB(NOW(), INTERVAL 7 DAY);"

echo "✅ Otimização do MySQL concluída!"
```

---

## 📊 Monitoramento de Performance

### 1. **Script de Análise de Performance** (`/opt/scripts/performance-analysis.sh`)
```bash
#!/bin/bash
# performance-analysis.sh - Análise detalhada de performance

REPORT_FILE="/var/log/performance-report-$(date +%Y%m%d).txt"

generate_report() {
    cat > $REPORT_FILE << EOF
=== RELATÓRIO DE PERFORMANCE - $(date) ===

=== SISTEMA ===
Uptime: $(uptime)
Load Average: $(cat /proc/loadavg)
CPU Info: $(grep "model name" /proc/cpuinfo | head -1 | cut -d: -f2)
Memory: $(free -h | grep Mem)
Disk Usage: $(df -h / | tail -1)

=== REDE ===
$(netstat -i)

=== PROCESSOS TOP ===
$(ps aux --sort=-%cpu | head -10)

=== MYSQL STATUS ===
$(mysql -u root -p$MYSQL_ROOT_PASSWORD -e "SHOW ENGINE INNODB STATUS\G" | grep -A 20 "BACKGROUND THREAD")

=== AZEROTHCORE LOGS (ÚLTIMAS 10 LINHAS) ===
$(tail -10 /opt/azerothcore/logs/Server.log)

=== CONEXÕES ATIVAS ===
AuthServer: $(netstat -an | grep :3724 | grep ESTABLISHED | wc -l)
WorldServer: $(netstat -an | grep :8085 | grep ESTABLISHED | wc -l)

=== JOGADORES ONLINE ===
$(mysql -u acore -p$DB_PASSWORD -e "SELECT COUNT(*) as 'Players Online' FROM characters.characters WHERE online=1;")

EOF

    echo "📊 Relatório gerado: $REPORT_FILE"
}

generate_report
```

---

**Configurações avançadas implementadas!** 🚀

Todas as configurações incluem:
- ✅ Otimizações de performance do sistema
- ✅ Segurança avançada com múltiplas camadas
- ✅ Tuning completo do MySQL
- ✅ Configurações otimizadas do AzerothCore
- ✅ Monitoramento detalhado de performance
