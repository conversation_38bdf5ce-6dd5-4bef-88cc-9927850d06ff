#!/bin/bash

LOG_FILE="/tmp/recovery_log.txt"

echo "$(date): Iniciando script de recuperação..." >> $LOG_FILE

# Tentar reiniciar Nginx
echo "$(date): Tentando reiniciar Nginx..." >> $LOG_FILE
/usr/bin/sudo /bin/systemctl restart nginx 2>&1 >> $LOG_FILE
if [ $? -eq 0 ]; then
    echo "$(date): Nginx reiniciado com systemctl." >> $LOG_FILE
else
    echo "$(date): systemctl falhou para Nginx. Tentando service..." >> $LOG_FILE
    /usr/sbin/sudo /usr/sbin/service nginx restart 2>&1 >> $LOG_FILE
    if [ $? -eq 0 ]; then
        echo "$(date): Nginx reiniciado com service." >> $LOG_FILE
    else
        echo "$(date): Falha ao reiniciar Nginx." >> $LOG_FILE
    fi
fi

# Tentar reiniciar PHP-FPM (assumindo php8.1-fpm)
echo "$(date): Tentando reiniciar PHP-FPM..." >> $LOG_FILE
/usr/bin/sudo /bin/systemctl restart php8.1-fpm 2>&1 >> $LOG_FILE
if [ $? -eq 0 ]; then
    echo "$(date): PHP-FPM reiniciado com systemctl." >> $LOG_FILE
else
    echo "$(date): systemctl falhou para PHP-FPM. Tentando service..." >> $LOG_FILE
    /usr/sbin/sudo /usr/sbin/service php8.1-fpm restart 2>&1 >> $LOG_FILE
    if [ $? -eq 0 ]; then
        echo "$(date): PHP-FPM reiniciado com service." >> $LOG_FILE
    else
        echo "$(date): Falha ao reiniciar PHP-FPM." >> $LOG_FILE
    fi
fi

echo "$(date): Script de recuperação concluído." >> $LOG_FILE
