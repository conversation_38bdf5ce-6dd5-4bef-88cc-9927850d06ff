# 🚨 REGRAS OBRIGATÓRIAS DO PROJETO - LEI<PERSON> ANTES DE COMEÇAR

## 🧪 **TESTES OBRIGATÓRIOS - EXECUTE ANTES DE QUALQUER ATIVIDADE**

### 🔑 **TESTES SSH OBRIGATÓRIOS (SEMPRE EXECUTAR PRIMEIRO):**
```bash
# 1. Teste básico de conexão
ssh azeroth-nexus "echo 'SSH OK'"

# 2. Informações do sistema
ssh azeroth-nexus "hostname && whoami && uptime"

# 3. Espaço em disco
ssh azeroth-nexus "df -h"

# 4. Status do Nginx
ssh azeroth-nexus "systemctl status nginx"

# 5. Site respondendo
ssh azeroth-nexus "curl -s -o /dev/null -w '%{http_code}' http://localhost"
```

### 🚀 **TESTES DEPLOY OBRIGATÓRIOS (SEMPRE EXECUTAR SEGUNDO):**
```bash
# 1. Deploy staging
cd wow-website && bash deploy/deploy.sh staging

# 2. Deploy produção
cd wow-website && bash deploy/deploy.sh production

# 3. Verificar site após deploy
ssh azeroth-nexus "curl -s http://localhost | head -5"
```

### ⚠️ **SE ALGUM TESTE FALHAR:**
- ❌ **PARE IMEDIATAMENTE**
- ❌ **NÃO CONTINUE COM NENHUMA ATIVIDADE**
- ❌ **REPORTE O PROBLEMA**
- ❌ **AGUARDE CORREÇÃO**

---

## ⚠️ REGRAS CRÍTICAS - NUNCA VIOLE ESTAS REGRAS

### 🔒 **PROIBIÇÕES ABSOLUTAS**

1. **❌ NUNCA ALTERE SEM PERMISSÃO:**
   - Layout do projeto
   - Estrutura de arquivos
   - Cores e design
   - Interface visual
   - Organização de pastas
   - Arquitetura do sistema

2. **❌ NUNCA FAÇA "JEITINHOS":**
   - Não simule que está funcionando
   - Não crie soluções temporárias
   - Não mascare problemas
   - Sempre resolva na raiz do problema

### ✅ **PROCEDIMENTOS OBRIGATÓRIOS**

#### 🔍 **ANTES DE COMEÇAR QUALQUER TRABALHO:**

1. **ANÁLISE COMPLETA DO PROJETO:**
   ```bash
   # Verificar estrutura atual
   - Entender arquitetura
   - Mapear dependências
   - Identificar componentes críticos
   - Documentar estado atual
   ```

2. **TESTES OBRIGATÓRIOS DE CONECTIVIDADE:**
   ```bash
   # ===== TESTES SSH OBRIGATÓRIOS =====
   # Teste 1: Conexão básica SSH sem senha
   ssh azeroth-nexus "echo 'Conexão SSH OK'"

   # Teste 2: Informações do sistema
   ssh azeroth-nexus "hostname && whoami && uptime"

   # Teste 3: Espaço em disco
   ssh azeroth-nexus "df -h"

   # Teste 4: Status dos serviços
   ssh azeroth-nexus "systemctl status nginx"

   # Teste 5: Site respondendo
   ssh azeroth-nexus "curl -s -o /dev/null -w '%{http_code}' http://localhost"

   # ===== TESTES DEPLOY OBRIGATÓRIOS =====
   # Teste 6: Deploy para staging
   cd wow-website && bash deploy/deploy.sh staging

   # Teste 7: Deploy para produção
   cd wow-website && bash deploy/deploy.sh production

   # Teste 8: Verificar site após deploy
   ssh azeroth-nexus "curl -s http://localhost | head -5"

   # ===== OUTROS TESTES =====
   # Verificar acesso ao banco (se necessário)
   ssh azeroth-nexus "mysql -u user -p -e 'SHOW DATABASES;'"

   # Testar APIs específicas (se necessário)
   curl -s http://************/api/stats.php
   ```

3. **SOLICITAR APROVAÇÃO:**
   - Explicar EXATAMENTE o que será alterado
   - Justificar a necessidade da mudança
   - Aguardar aprovação explícita
   - Documentar o plano de ação

#### 🛠️ **DURANTE O DESENVOLVIMENTO:**

1. **FOCO NO PROBLEMA REAL:**
   - Identificar causa raiz
   - Resolver o problema específico
   - Não criar soluções paralelas
   - Manter simplicidade

2. **NOMENCLATURA DE ARQUIVOS DE TESTE:**
   ```
   ✅ CORRETO:
   - test_conexao.php
   - debug_stats.js
   - temp_backup.sql
   - teste_api.html
   
   ❌ ERRADO:
   - arquivo.php
   - novo.js
   - backup.sql
   - index2.html
   ```

#### 🚀 **APÓS COMPLETAR ALTERAÇÕES:**

1. **TESTES OBRIGATÓRIOS PÓS-ALTERAÇÃO:**
   ```bash
   # ===== TESTES SSH PÓS-ALTERAÇÃO =====
   # Verificar se SSH ainda funciona
   ssh azeroth-nexus "echo 'SSH ainda funcionando'"

   # Verificar serviços críticos
   ssh azeroth-nexus "systemctl status nginx mysql"

   # ===== TESTES DEPLOY PÓS-ALTERAÇÃO =====
   # Fazer deploy das alterações
   cd wow-website && bash deploy/deploy.sh production

   # Verificar se site ainda funciona
   ssh azeroth-nexus "curl -s -o /dev/null -w '%{http_code}' http://localhost"

   # ===== TESTES FUNCIONAIS =====
   # Testar funcionalidade alterada
   curl -s http://************/

   # Verificar APIs específicas
   curl -s http://************/api/stats.php

   # Verificar logs de erro
   ssh azeroth-nexus "tail -10 /var/log/nginx/error.log"
   ```

2. **LIMPEZA DO PROJETO:**
   ```bash
   # Remover arquivos de teste
   rm test_*.php
   rm debug_*.js
   rm temp_*.sql
   rm teste_*.html
   ```

3. **DEPLOY OBRIGATÓRIO:**
   - Sempre fazer deploy após alterações
   - Verificar se deploy foi bem-sucedido
   - Testar em produção
   - Confirmar funcionamento

### 📋 **CHECKLIST OBRIGATÓRIO**

#### ✅ **ANTES DE INICIAR:**
- [ ] Analisei completamente o projeto atual
- [ ] Entendi a arquitetura e funcionamento
- [ ] **EXECUTEI TODOS OS TESTES SSH OBRIGATÓRIOS:**
  - [ ] `ssh azeroth-nexus "echo 'Conexão SSH OK'"` ✅
  - [ ] `ssh azeroth-nexus "hostname && whoami && uptime"` ✅
  - [ ] `ssh azeroth-nexus "df -h"` ✅
  - [ ] `ssh azeroth-nexus "systemctl status nginx"` ✅
  - [ ] `ssh azeroth-nexus "curl -s -o /dev/null -w '%{http_code}' http://localhost"` ✅
- [ ] **EXECUTEI TODOS OS TESTES DEPLOY OBRIGATÓRIOS:**
  - [ ] `cd wow-website && bash deploy/deploy.sh staging` ✅
  - [ ] `cd wow-website && bash deploy/deploy.sh production` ✅
  - [ ] `ssh azeroth-nexus "curl -s http://localhost | head -5"` ✅
- [ ] Identifiquei o problema real na raiz
- [ ] Expliquei o que será alterado
- [ ] Recebi aprovação explícita para mudanças

#### ✅ **DURANTE O TRABALHO:**
- [ ] Foquei apenas no problema específico
- [ ] Não alterei layout/design sem permissão
- [ ] Usei nomenclatura correta para arquivos de teste
- [ ] Resolvi o problema na raiz, não mascarei

#### ✅ **APÓS COMPLETAR:**
- [ ] **EXECUTEI TODOS OS TESTES SSH PÓS-ALTERAÇÃO:**
  - [ ] `ssh azeroth-nexus "echo 'SSH ainda funcionando'"` ✅
  - [ ] `ssh azeroth-nexus "systemctl status nginx mysql"` ✅
- [ ] **EXECUTEI TODOS OS TESTES DEPLOY PÓS-ALTERAÇÃO:**
  - [ ] `cd wow-website && bash deploy/deploy.sh production` ✅
  - [ ] `ssh azeroth-nexus "curl -s -o /dev/null -w '%{http_code}' http://localhost"` ✅
- [ ] **EXECUTEI TESTES FUNCIONAIS:**
  - [ ] `curl -s http://************/` ✅
  - [ ] `curl -s http://************/api/stats.php` ✅
  - [ ] `ssh azeroth-nexus "tail -10 /var/log/nginx/error.log"` ✅
- [ ] Testei a funcionalidade específica alterada
- [ ] Verifiquei se não quebrei outras funcionalidades
- [ ] Removi todos os arquivos de teste
- [ ] Fiz deploy das alterações
- [ ] Testei em produção
- [ ] Confirmei que está funcionando 100%

### 🚨 **CONSEQUÊNCIAS DE VIOLAÇÃO**

**Se violar estas regras:**
- ❌ Trabalho será rejeitado
- ❌ Alterações serão revertidas
- ❌ Projeto pode ser danificado
- ❌ Perda de confiança

### 💡 **PRINCÍPIOS FUNDAMENTAIS**

1. **TRANSPARÊNCIA TOTAL:**
   - Sempre explicar o que será feito
   - Documentar mudanças
   - Comunicar problemas encontrados

2. **QUALIDADE ACIMA DE VELOCIDADE:**
   - Fazer certo na primeira vez
   - Testar exaustivamente
   - Não deixar problemas para depois

3. **RESPEITO AO PROJETO:**
   - Manter integridade do código
   - Preservar arquitetura existente
   - Não fazer mudanças desnecessárias

---

## 📞 **EM CASO DE DÚVIDAS**

**SEMPRE PERGUNTE ANTES DE:**
- Alterar qualquer aspecto visual
- Modificar estrutura de arquivos
- Implementar nova funcionalidade
- Fazer mudanças na arquitetura

**LEMBRE-SE:** É melhor perguntar 10 vezes do que quebrar o projeto uma vez!

---

## 📋 **REFERÊNCIA RÁPIDA - TESTES OBRIGATÓRIOS**

### 🔥 **COMANDOS ESSENCIAIS (COPIE E COLE):**

```bash
# ===== TESTES SSH (EXECUTAR SEMPRE PRIMEIRO) =====
ssh azeroth-nexus "echo 'SSH OK'"
ssh azeroth-nexus "hostname && whoami && uptime"
ssh azeroth-nexus "df -h"
ssh azeroth-nexus "systemctl status nginx"
ssh azeroth-nexus "curl -s -o /dev/null -w '%{http_code}' http://localhost"

# ===== TESTES DEPLOY (EXECUTAR SEMPRE SEGUNDO) =====
cd wow-website && bash deploy/deploy.sh staging
cd wow-website && bash deploy/deploy.sh production
ssh azeroth-nexus "curl -s http://localhost | head -5"
```

### 🎯 **RESULTADO ESPERADO:**
- ✅ SSH: "SSH OK", hostname "Azeroth-Nexus", nginx "active", HTTP "200"
- ✅ Deploy: Upload de arquivos, permissões configuradas, site funcionando
- ✅ Site: HTML do site carregando corretamente

### ❌ **SE ALGO FALHAR:**
1. **PARE TUDO**
2. **REPORTE O ERRO**
3. **NÃO CONTINUE**

---

*Este documento deve ser lido e seguido rigorosamente por qualquer pessoa que trabalhe neste projeto.*
