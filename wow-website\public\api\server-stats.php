<?php
/**
 * API - Estatísticas do Servidor
 *
 * <AUTHOR> Agent
 * @version 1.0
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

// Incluir configurações
require_once '../../includes/config/config.php';

// Função isGetRequest() já está definida em security.php

/**
 * Obter IP real do cliente
 */
function getRealIP() {
    if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
        return $_SERVER['HTTP_CLIENT_IP'];
    } elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
        return $_SERVER['HTTP_X_FORWARDED_FOR'];
    } else {
        return $_SERVER['REMOTE_ADDR'];
    }
}

/**
 * Verificar rate limit simples
 */
function checkRateLimit($key, $limit, $window) {
    // Implementação simples de rate limiting
    // Em produção, usar Redis ou banco de dados
    return true; // Por enquanto, sempre permitir
}

/**
 * Retornar resposta JSON
 */
function jsonResponse($data, $code = 200) {
    http_response_code($code);
    echo json_encode($data, JSON_UNESCAPED_UNICODE);
    exit;
}

// Verificar se é requisição GET
if (!isGetRequest()) {
    http_response_code(405);
    jsonResponse(['error' => 'Método não permitido'], 405);
}

// Rate limiting para API
$client_ip = getRealIP();
if (!checkRateLimit("api_stats_{$client_ip}", 60, 60)) { // 60 requests por minuto
    http_response_code(429);
    jsonResponse(['error' => 'Rate limit excedido'], 429);
}

try {
    // Tentar obter estatísticas reais do servidor
    $stats = DatabaseManager::getServerStats();

    // Se não conseguir conectar, retornar dados offline reais
    if (!$stats['online']) {
        $stats = [
            'online' => false,
            'players_online' => 0,
            'total_accounts' => 0,
            'total_characters' => 0,
            'uptime' => '0d 0h 0m'
        ];
    }

    // Adicionar informações extras
    $extra_stats = [
        'server_name' => 'AzerothNexus',
        'server_version' => SERVER_VERSION,
        'server_type' => SERVER_TYPE,
        'rates' => SERVER_RATES,
        'timestamp' => time(),
        'last_update' => date('Y-m-d H:i:s')
    ];

    // Combinar estatísticas
    $response = array_merge($stats, $extra_stats);

    // Log da requisição (apenas em desenvolvimento)
    if (($_ENV['APP_ENV'] ?? 'development') === 'development') {
        writeLog('DEBUG', 'API server-stats called', [
            'ip' => $client_ip,
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
            'stats' => $response
        ]);
    }

    // Retornar resposta
    jsonResponse($response);

} catch (Exception $e) {
    writeLog('ERROR', 'API server-stats error: ' . $e->getMessage(), [
        'ip' => $client_ip,
        'trace' => $e->getTraceAsString()
    ]);

    // Retornar dados offline reais em caso de erro
    http_response_code(200); // Não é erro do cliente
    jsonResponse([
        'error' => 'Servidor offline',
        'online' => false,
        'players_online' => 0,
        'total_accounts' => 0,
        'total_characters' => 0,
        'uptime' => '0d 0h 0m',
        'server_name' => 'AzerothNexus',
        'server_version' => SERVER_VERSION,
        'server_type' => SERVER_TYPE,
        'rates' => SERVER_RATES,
        'timestamp' => time(),
        'last_update' => date('Y-m-d H:i:s')
    ]);
}
?>
