<?php
/**
 * Configuração Principal do Site
 * 
 * <AUTHOR> Agent
 * @version 1.0
 */

// Iniciar sessão
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Configurações de erro
error_reporting(E_ALL);
ini_set('display_errors', ($_ENV['APP_ENV'] ?? 'production') === 'development' ? 1 : 0);

// Timezone
date_default_timezone_set('America/Sao_Paulo');

// Configurações do site
define('SITE_NAME', 'AzerothNexus Server');
define('SITE_DESCRIPTION', 'Servidor World of Warcraft 3.3.5a - A melhor experiência WotLK');
define('SITE_URL', $_ENV['SITE_URL'] ?? 'https://seuservidor.com');
define('SITE_VERSION', '1.0.0');

// Configurações do servidor
define('SERVER_NAME', 'AzerothNexus');
define('SERVER_VERSION', '3.3.5a');
define('SERVER_EXPANSION', 'Wrath of the Lich King');
define('SERVER_TYPE', 'PvP'); // PvP, PvE, RP
define('SERVER_RATES', [
    'xp' => 1,
    'drop' => 1,
    'money' => 1,
    'honor' => 1
]);

// Configurações de segurança
define('CSRF_TOKEN_NAME', 'csrf_token');
define('SESSION_TIMEOUT', 3600); // 1 hora
define('MAX_LOGIN_ATTEMPTS', 5);
define('LOGIN_LOCKOUT_TIME', 900); // 15 minutos

// Configurações de upload
define('UPLOAD_MAX_SIZE', 5 * 1024 * 1024); // 5MB
define('ALLOWED_EXTENSIONS', ['jpg', 'jpeg', 'png', 'gif']);

// Configurações de email
define('SMTP_HOST', $_ENV['SMTP_HOST'] ?? 'localhost');
define('SMTP_PORT', $_ENV['SMTP_PORT'] ?? 587);
define('SMTP_USER', $_ENV['SMTP_USER'] ?? '');
define('SMTP_PASS', $_ENV['SMTP_PASS'] ?? '');
define('SMTP_FROM', $_ENV['SMTP_FROM'] ?? '<EMAIL>');

// Configurações de cache
define('CACHE_ENABLED', true);
define('CACHE_TTL', 300); // 5 minutos

// Configurações de API
define('API_RATE_LIMIT', 100); // requests por minuto
define('API_VERSION', 'v1');

// Configurações de doações
define('DONATIONS_ENABLED', true);
define('PAYPAL_CLIENT_ID', $_ENV['PAYPAL_CLIENT_ID'] ?? '');
define('PAYPAL_SECRET', $_ENV['PAYPAL_SECRET'] ?? '');
define('PIX_KEY', $_ENV['PIX_KEY'] ?? '');

// Configurações de logs
define('LOG_LEVEL', $_ENV['LOG_LEVEL'] ?? 'INFO');
define('LOG_FILE', __DIR__ . '/../../logs/website.log');

// Configurações de paths
define('ROOT_PATH', dirname(dirname(dirname(__FILE__))));
define('PUBLIC_PATH', ROOT_PATH . '/public');
define('INCLUDES_PATH', ROOT_PATH . '/includes');
define('TEMPLATES_PATH', ROOT_PATH . '/templates');
define('ASSETS_PATH', '/assets');
define('UPLOADS_PATH', PUBLIC_PATH . '/uploads');

// Autoload de classes
spl_autoload_register(function ($class) {
    $file = INCLUDES_PATH . '/classes/' . $class . '.php';
    if (file_exists($file)) {
        require_once $file;
    }
});

// Incluir arquivos essenciais
require_once INCLUDES_PATH . '/config/database.php';
require_once INCLUDES_PATH . '/functions/security.php';
require_once INCLUDES_PATH . '/functions/helpers.php';

// Configurações de headers de segurança
if (!headers_sent()) {
    header('X-Content-Type-Options: nosniff');
    header('X-Frame-Options: DENY');
    header('X-XSS-Protection: 1; mode=block');
    header('Referrer-Policy: strict-origin-when-cross-origin');
    
    if (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on') {
        header('Strict-Transport-Security: max-age=31536000; includeSubDomains');
    }
}

// Função para debug (apenas em desenvolvimento)
function debug($data, $die = false) {
    if ($_ENV['APP_ENV'] === 'development') {
        echo '<pre>';
        print_r($data);
        echo '</pre>';
        if ($die) die();
    }
}

// Função para log
function writeLog($level, $message, $context = []) {
    $levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL'];
    
    if (array_search($level, $levels) >= array_search(LOG_LEVEL, $levels)) {
        $timestamp = date('Y-m-d H:i:s');
        $contextStr = !empty($context) ? ' ' . json_encode($context) : '';
        $logMessage = "[$timestamp] [$level] $message$contextStr" . PHP_EOL;
        
        // Criar diretório de logs se não existir
        $logDir = dirname(LOG_FILE);
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
        
        file_put_contents(LOG_FILE, $logMessage, FILE_APPEND | LOCK_EX);
    }
}

// Tratamento de erros
set_error_handler(function($severity, $message, $file, $line) {
    if (!(error_reporting() & $severity)) {
        return false;
    }
    
    $errorTypes = [
        E_ERROR => 'ERROR',
        E_WARNING => 'WARNING',
        E_NOTICE => 'INFO',
        E_USER_ERROR => 'ERROR',
        E_USER_WARNING => 'WARNING',
        E_USER_NOTICE => 'INFO'
    ];
    
    $level = $errorTypes[$severity] ?? 'ERROR';
    writeLog($level, "PHP Error: $message in $file:$line");
    
    if ($_ENV['APP_ENV'] === 'development') {
        echo "<b>$level:</b> $message in <b>$file</b> on line <b>$line</b><br>";
    }
    
    return true;
});

// Tratamento de exceções não capturadas
set_exception_handler(function($exception) {
    writeLog('CRITICAL', 'Uncaught Exception: ' . $exception->getMessage(), [
        'file' => $exception->getFile(),
        'line' => $exception->getLine(),
        'trace' => $exception->getTraceAsString()
    ]);
    
    if ($_ENV['APP_ENV'] === 'development') {
        echo '<h1>Uncaught Exception</h1>';
        echo '<p><strong>Message:</strong> ' . $exception->getMessage() . '</p>';
        echo '<p><strong>File:</strong> ' . $exception->getFile() . '</p>';
        echo '<p><strong>Line:</strong> ' . $exception->getLine() . '</p>';
        echo '<pre>' . $exception->getTraceAsString() . '</pre>';
    } else {
        echo '<h1>Erro Interno do Servidor</h1>';
        echo '<p>Ocorreu um erro inesperado. Tente novamente mais tarde.</p>';
    }
});

// Verificar se o site está em manutenção
if (file_exists(ROOT_PATH . '/.maintenance') && !isset($_SESSION['admin_logged'])) {
    http_response_code(503);
    include TEMPLATES_PATH . '/maintenance.php';
    exit;
}

// Inicializar sistema de cache simples
class SimpleCache {
    private static $cache = [];
    
    public static function get($key) {
        if (isset(self::$cache[$key])) {
            $item = self::$cache[$key];
            if ($item['expires'] > time()) {
                return $item['data'];
            } else {
                unset(self::$cache[$key]);
            }
        }
        return null;
    }
    
    public static function set($key, $data, $ttl = CACHE_TTL) {
        self::$cache[$key] = [
            'data' => $data,
            'expires' => time() + $ttl
        ];
    }
    
    public static function delete($key) {
        unset(self::$cache[$key]);
    }
    
    public static function clear() {
        self::$cache = [];
    }
}

// Log de inicialização
writeLog('INFO', 'Sistema inicializado', [
    'environment' => $_ENV['APP_ENV'] ?? 'development',
    'user_ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
    'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
]);

?>
