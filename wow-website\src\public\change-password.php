<?php
/**
 * Página de Alteração de Senha
 * 
 * <AUTHOR> Agent
 * @version 1.0
 */

require_once '../includes/config/config.php';

// Verificar se está logado
if (!isLoggedIn()) {
    redirect('/login?redirect=' . urlencode('/change-password'));
}

$errors = [];
$success = false;

// Processar formulário
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Verificar CSRF
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $errors[] = 'Token de segurança inválido.';
    } else {
        $current_password = $_POST['current_password'] ?? '';
        $new_password = $_POST['new_password'] ?? '';
        $confirm_password = $_POST['confirm_password'] ?? '';
        
        // Validações
        if (empty($current_password)) {
            $errors[] = 'Senha atual é obrigatória.';
        }
        
        if (empty($new_password)) {
            $errors[] = 'Nova senha é obrigatória.';
        } elseif (strlen($new_password) < 6) {
            $errors[] = 'Nova senha deve ter pelo menos 6 caracteres.';
        }
        
        if ($new_password !== $confirm_password) {
            $errors[] = 'Confirmação de senha não confere.';
        }
        
        if ($current_password === $new_password) {
            $errors[] = 'A nova senha deve ser diferente da atual.';
        }
        
        // Verificar senha atual
        if (empty($errors)) {
            try {
                $auth_db = DatabaseManager::getConnection('auth');
                $stmt = $auth_db->prepare("SELECT sha_pass_hash FROM account WHERE id = ?");
                $stmt->execute([$_SESSION['user_id']]);
                $account = $stmt->fetch();
                
                if ($account) {
                    $current_hash = strtoupper(sha1(strtoupper($_SESSION['username']) . ':' . strtoupper($current_password)));
                    
                    if (!hash_equals($account['sha_pass_hash'], $current_hash)) {
                        $errors[] = 'Senha atual incorreta.';
                    }
                } else {
                    $errors[] = 'Conta não encontrada.';
                }
                
            } catch (Exception $e) {
                $errors[] = 'Erro ao verificar senha atual.';
                writeLog('ERROR', 'Change password verification error: ' . $e->getMessage());
            }
        }
        
        // Alterar senha se não há erros
        if (empty($errors)) {
            try {
                $auth_db = DatabaseManager::getConnection('auth');
                $new_hash = strtoupper(sha1(strtoupper($_SESSION['username']) . ':' . strtoupper($new_password)));
                
                $stmt = $auth_db->prepare("UPDATE account SET sha_pass_hash = ? WHERE id = ?");
                $stmt->execute([$new_hash, $_SESSION['user_id']]);
                
                $success = true;
                writeLog('INFO', 'Password changed', [
                    'username' => $_SESSION['username'],
                    'ip' => getRealIP()
                ]);
                
            } catch (Exception $e) {
                $errors[] = 'Erro ao alterar senha. Tente novamente.';
                writeLog('ERROR', 'Change password error: ' . $e->getMessage());
            }
        }
    }
}

// Dados para o template
$page_data = [
    'title' => 'Alterar Senha - ' . SITE_NAME,
    'description' => 'Alterar senha da conta',
    'current_page' => 'change-password'
];

?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= escape($page_data['title']) ?></title>
    <meta name="description" content="<?= escape($page_data['description']) ?>">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="<?= ASSETS_PATH ?>/css/style.css" rel="stylesheet">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?= ASSETS_PATH ?>/images/favicon.ico">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark fixed-top">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-shield-alt me-2"></i>
                <?= SITE_NAME ?>
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">Início</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/download">Download</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/ranking">Ranking</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/forum">Fórum</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/donate">Doações</a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user"></i> <?= escape($_SESSION['username']) ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/account">Minha Conta</a></li>
                            <li><a class="dropdown-item" href="/characters">Personagens</a></li>
                            <?php if (isAdmin()): ?>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="/admin">Painel Admin</a></li>
                            <?php endif; ?>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="/logout">Sair</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container mt-5 pt-5">
        <div class="row justify-content-center">
            <div class="col-md-6 col-lg-5">
                <div class="card shadow">
                    <div class="card-header bg-warning text-dark text-center">
                        <h4 class="mb-0">
                            <i class="fas fa-key"></i> Alterar Senha
                        </h4>
                    </div>
                    <div class="card-body">
                        <?php if ($success): ?>
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle"></i>
                                <strong>Senha alterada com sucesso!</strong><br>
                                Sua senha foi atualizada. Use a nova senha no próximo login.
                            </div>
                            <div class="text-center">
                                <a href="/account" class="btn btn-primary">
                                    <i class="fas fa-user"></i> Voltar à Conta
                                </a>
                            </div>
                        <?php else: ?>
                            <?php if (!empty($errors)): ?>
                                <div class="alert alert-danger">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    <strong>Erro(s) encontrado(s):</strong>
                                    <ul class="mb-0 mt-2">
                                        <?php foreach ($errors as $error): ?>
                                            <li><?= escape($error) ?></li>
                                        <?php endforeach; ?>
                                    </ul>
                                </div>
                            <?php endif; ?>
                            
                            <form method="POST" action="/change-password">
                                <input type="hidden" name="csrf_token" value="<?= generateCSRFToken() ?>">
                                
                                <div class="mb-3">
                                    <label for="current_password" class="form-label">Senha Atual</label>
                                    <input type="password" class="form-control" id="current_password" name="current_password" required>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="new_password" class="form-label">Nova Senha</label>
                                    <input type="password" class="form-control" id="new_password" name="new_password" 
                                           required minlength="6">
                                    <div class="form-text">Mínimo 6 caracteres</div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="confirm_password" class="form-label">Confirmar Nova Senha</label>
                                    <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                                </div>
                                
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-warning">
                                        <i class="fas fa-key"></i> Alterar Senha
                                    </button>
                                </div>
                            </form>
                        <?php endif; ?>
                        
                        <hr>
                        <div class="text-center">
                            <a href="/account" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left"></i> Voltar à Conta
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Validação do formulário -->
    <script>
        document.getElementById('confirm_password').addEventListener('input', function() {
            const newPassword = document.getElementById('new_password').value;
            const confirmPassword = this.value;
            
            if (newPassword !== confirmPassword) {
                this.setCustomValidity('Senhas não coincidem');
            } else {
                this.setCustomValidity('');
            }
        });
    </script>
</body>
</html>
