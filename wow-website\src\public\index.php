<?php
/**
 * <PERSON><PERSON>gina Principal do Site
 * 
 * <AUTHOR> Agent
 * @version 1.0
 */

require_once '../includes/config/config.php';

// Obter estatísticas do servidor
$server_stats = getServerStatsCache();
$top_players = getTopPlayers(5);

// Dados para o template
$page_data = [
    'title' => 'Início - ' . SITE_NAME,
    'description' => SITE_DESCRIPTION,
    'server_stats' => $server_stats,
    'top_players' => $top_players,
    'current_page' => 'home'
];

?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= escape($page_data['title']) ?></title>
    <meta name="description" content="<?= escape($page_data['description']) ?>">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="<?= ASSETS_PATH ?>/css/style.css" rel="stylesheet">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?= ASSETS_PATH ?>/images/favicon.ico">
    
    <!-- Open Graph -->
    <meta property="og:title" content="<?= escape($page_data['title']) ?>">
    <meta property="og:description" content="<?= escape($page_data['description']) ?>">
    <meta property="og:image" content="<?= SITE_URL ?><?= ASSETS_PATH ?>/images/og-image.jpg">
    <meta property="og:url" content="<?= SITE_URL ?>">
    <meta property="og:type" content="website">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark fixed-top">
        <div class="container">
            <a class="navbar-brand" href="/">
                <img src="<?= ASSETS_PATH ?>/images/logo.png" alt="Logo" height="40">
                <span class="ms-2"><?= SITE_NAME ?></span>
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="/">Início</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/download">Download</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/ranking">Ranking</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/forum">Fórum</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/donate">Doações</a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <?php if (isLoggedIn()): ?>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user"></i> <?= escape($_SESSION['username']) ?>
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="/account">Minha Conta</a></li>
                                <li><a class="dropdown-item" href="/characters">Personagens</a></li>
                                <?php if (isAdmin()): ?>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="/admin">Painel Admin</a></li>
                                <?php endif; ?>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="/logout">Sair</a></li>
                            </ul>
                        </li>
                    <?php else: ?>
                        <li class="nav-item">
                            <a class="nav-link" href="/login">Login</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/register">Registrar</a>
                        </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-section">
        <div class="hero-overlay"></div>
        <div class="container">
            <div class="row align-items-center min-vh-100">
                <div class="col-lg-6">
                    <div class="hero-content text-white">
                        <h1 class="display-4 fw-bold mb-4">
                            A melhor aventura em World of Warcraft <?= SERVER_VERSION ?>
                        </h1>
                        <p class="lead mb-4">
                            Servidor estável, comunidade ativa e diversão garantida!
                        </p>
                        <div class="hero-buttons">
                            <a href="/register" class="btn btn-primary btn-lg me-3">
                                <i class="fas fa-user-plus"></i> Criar Conta
                            </a>
                            <a href="/download" class="btn btn-outline-light btn-lg">
                                <i class="fas fa-download"></i> Download
                            </a>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="server-status-card">
                        <div class="card bg-dark text-white">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-server"></i> Status do Servidor
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row text-center">
                                    <div class="col-6">
                                        <div class="stat-item">
                                            <div class="stat-value">
                                                <?php if ($server_stats['online']): ?>
                                                    <span class="text-success">
                                                        <i class="fas fa-circle"></i> ONLINE
                                                    </span>
                                                <?php else: ?>
                                                    <span class="text-danger">
                                                        <i class="fas fa-circle"></i> OFFLINE
                                                    </span>
                                                <?php endif; ?>
                                            </div>
                                            <div class="stat-label">Status</div>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="stat-item">
                                            <div class="stat-value text-primary">
                                                <?= formatNumber($server_stats['players_online']) ?>
                                            </div>
                                            <div class="stat-label">Jogadores Online</div>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="stat-item">
                                            <div class="stat-value text-info">
                                                <?= formatNumber($server_stats['total_accounts']) ?>
                                            </div>
                                            <div class="stat-label">Contas Registradas</div>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="stat-item">
                                            <div class="stat-value text-warning">
                                                <?= $server_stats['uptime'] ?>
                                            </div>
                                            <div class="stat-label">Uptime</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="features-section py-5">
        <div class="container">
            <div class="row">
                <div class="col-lg-12 text-center mb-5">
                    <h2 class="section-title">Por que escolher nosso servidor?</h2>
                    <p class="section-subtitle">Características que nos tornam únicos</p>
                </div>
            </div>
            <div class="row">
                <div class="col-md-4 mb-4">
                    <div class="feature-card text-center">
                        <div class="feature-icon">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <h4>Estabilidade</h4>
                        <p>Servidor com 99.9% de uptime, backups automáticos e infraestrutura robusta.</p>
                    </div>
                </div>
                <div class="col-md-4 mb-4">
                    <div class="feature-card text-center">
                        <div class="feature-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <h4>Comunidade Ativa</h4>
                        <p>Milhares de jogadores ativos, eventos regulares e suporte 24/7.</p>
                    </div>
                </div>
                <div class="col-md-4 mb-4">
                    <div class="feature-card text-center">
                        <div class="feature-icon">
                            <i class="fas fa-cog"></i>
                        </div>
                        <h4>Rates Balanceadas</h4>
                        <p>XP: <?= SERVER_RATES['xp'] ?>x | Drop: <?= SERVER_RATES['drop'] ?>x | Experiência autêntica.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Top Players Section -->
    <?php if (!empty($top_players)): ?>
    <section class="top-players-section py-5 bg-light">
        <div class="container">
            <div class="row">
                <div class="col-lg-12 text-center mb-5">
                    <h2 class="section-title">Top Jogadores</h2>
                    <p class="section-subtitle">Os heróis mais poderosos do servidor</p>
                </div>
            </div>
            <div class="row">
                <div class="col-lg-8 mx-auto">
                    <div class="card">
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Rank</th>
                                            <th>Nome</th>
                                            <th>Level</th>
                                            <th>Classe</th>
                                            <th>Raça</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($top_players as $index => $player): ?>
                                        <tr>
                                            <td>
                                                <span class="rank-badge rank-<?= $index + 1 ?>">
                                                    #<?= $index + 1 ?>
                                                </span>
                                            </td>
                                            <td>
                                                <strong style="color: <?= getClassColor($player['class']) ?>">
                                                    <?= escape($player['name']) ?>
                                                </strong>
                                            </td>
                                            <td>
                                                <span class="badge bg-primary"><?= $player['level'] ?></span>
                                            </td>
                                            <td><?= getClassName($player['class']) ?></td>
                                            <td>
                                                <span class="faction-<?= getFactionIcon($player['race']) ?>">
                                                    <?= getRaceName($player['race']) ?>
                                                </span>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                            <div class="text-center mt-3">
                                <a href="/ranking" class="btn btn-primary">Ver Ranking Completo</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <?php endif; ?>

    <!-- Footer -->
    <footer class="footer bg-dark text-white py-4">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5><?= SITE_NAME ?></h5>
                    <p>O melhor servidor World of Warcraft <?= SERVER_VERSION ?> do Brasil.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <div class="social-links">
                        <a href="#" class="text-white me-3"><i class="fab fa-discord"></i></a>
                        <a href="#" class="text-white me-3"><i class="fab fa-facebook"></i></a>
                        <a href="#" class="text-white"><i class="fab fa-youtube"></i></a>
                    </div>
                    <p class="mt-2 mb-0">
                        &copy; <?= date('Y') ?> <?= SITE_NAME ?>. Todos os direitos reservados.
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="<?= ASSETS_PATH ?>/js/main.js"></script>
    
    <!-- Auto-refresh server stats -->
    <script>
        setInterval(function() {
            fetch('/api/server-stats.php')
                .then(response => response.json())
                .then(data => {
                    // Atualizar estatísticas em tempo real
                    document.querySelector('.stat-value .text-primary').textContent = data.players_online;
                })
                .catch(error => console.log('Erro ao atualizar stats:', error));
        }, 30000); // Atualizar a cada 30 segundos
    </script>
</body>
</html>
