# Script PowerShell para fazer o deploy das imagens para o servidor
# Este script deve ser executado após a criação das imagens

# Configurações
$scriptDir = $PSScriptRoot
$environment = "staging" # Pode ser: development, staging, production

# Função para exibir mensagens coloridas
function Write-ColorOutput {
    param (
        [Parameter(Mandatory=$true)]
        [string]$Message,
        
        [Parameter(Mandatory=$false)]
        [string]$ForegroundColor = "White"
    )
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    Write-Host "[$timestamp] " -ForegroundColor Cyan -NoNewline
    Write-Host $Message -ForegroundColor $ForegroundColor
}

# Configurações por ambiente
switch ($environment) {
    "development" {
        $serverHost = "localhost"
        $serverUser = "www-data"
        $serverPath = "/var/www/html/wow-dev"
        $sshKey = ""
    }
    "staging" {
        $serverHost = "************" # IP do servidor Azeroth-Nexus
        $serverUser = "root"
        $serverPath = "/var/www/html/wow-staging"
        $sshKey = "$env:USERPROFILE\.ssh\azeroth-nexus"
    }
    "production" {
        $serverHost = "************" # IP do servidor Azeroth-Nexus
        $serverUser = "root"
        $serverPath = "/var/www/html"
        $sshKey = "$env:USERPROFILE\.ssh\azeroth-nexus"
    }
    default {
        Write-ColorOutput "Ambiente inválido: $environment. Use: development, staging, production" "Red"
        exit 1
    }
}

# Verificar se estamos no diretório correto
if (-not (Test-Path "$scriptDir\src\public\assets\images")) {
    Write-ColorOutput "Diretório de imagens não encontrado. Execute o script do diretório correto." "Red"
    exit 1
}

# Verificar dependências
try {
    $null = Get-Command ssh -ErrorAction Stop
    $null = Get-Command scp -ErrorAction Stop
} catch {
    Write-ColorOutput "SSH ou SCP não estão instalados. Instale o OpenSSH Client." "Red"
    exit 1
}

# Função para fazer o deploy das imagens
function Deploy-Images {
    Write-ColorOutput "Fazendo deploy das imagens para $serverHost..." "Yellow"
    
    # Testar conexão SSH
    try {
        $null = ssh -i "$sshKey" -o "ConnectTimeout=10" "$serverUser@$serverHost" "echo 'Conexão OK'" 2>$null
    } catch {
        Write-ColorOutput "Não foi possível conectar ao servidor remoto" "Yellow"
        Write-ColorOutput "Verifique se o container está rodando e a chave SSH está configurada" "Yellow"
        return
    }
    
    # Criar diretório de imagens no servidor
    Write-ColorOutput "Criando diretorio de imagens no servidor..." "Yellow"
    ssh -i "$sshKey" "$serverUser@$serverHost" "mkdir -p '$serverPath/public/assets/images'"
    
    # Upload das imagens
    Write-ColorOutput "Enviando imagens..." "Yellow"
    $imageFiles = Get-ChildItem "$scriptDir\build\public\assets\images\*" -File
    foreach ($file in $imageFiles) {
        Write-Host "Enviando $($file.Name)..." -ForegroundColor Gray
        $remotePath = "$serverUser@$serverHost`://$serverPath/public/assets/images/"
        scp -i "$sshKey" $file.FullName $remotePath
    }
    
    # Configurar permissões remotamente
    Write-ColorOutput "Configurando permissoes..." "Yellow"
    ssh -i "$sshKey" "$serverUser@$serverHost" @"
    chown -R www-data:www-data '$serverPath/public/assets/images'
    chmod -R 755 '$serverPath/public/assets/images'
"@
    
    # Verificar se as imagens foram transferidas corretamente
    Write-ColorOutput "Verificando imagens..." "Yellow"
    ssh -i "$sshKey" "$serverUser@$serverHost" "ls -la '$serverPath/public/assets/images/'"
    
    Write-ColorOutput "Deploy das imagens concluido com sucesso!" "Green"
}

# Executar o deploy das imagens
Deploy-Images