<?php
/**
 * Teste Direto de Login
 */

// Simular POST
$_POST['username'] = 'cavalcrod';
$_POST['password'] = '200381';
$_POST['csrf_token'] = 'test';
$_SERVER['REQUEST_METHOD'] = 'POST';

// Iniciar sessão
session_start();
$_SESSION['csrf_token'] = 'test';

echo "=== TESTE DIRETO DE LOGIN ===\n";
echo "Username: " . $_POST['username'] . "\n";
echo "Password: " . $_POST['password'] . "\n";

// Incluir o arquivo de login
ob_start();
try {
    include '/var/www/html/public/login.php';
    $output = ob_get_contents();
} catch (Exception $e) {
    echo "ERRO: " . $e->getMessage() . "\n";
    $output = ob_get_contents();
} finally {
    ob_end_clean();
}

echo "=== RESULTADO ===\n";
if (isset($_SESSION['user_id'])) {
    echo "✅ LOGIN SUCESSO!\n";
    echo "User ID: " . $_SESSION['user_id'] . "\n";
    echo "Username: " . $_SESSION['username'] . "\n";
} else {
    echo "❌ LOGIN FALHOU!\n";
    echo "Output: " . substr($output, 0, 500) . "\n";
}
?>
