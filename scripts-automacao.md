# 🤖 Scripts de Automação - AzerothCore

## 📋 Índice
1. [Scripts de Monitoramento](#scripts-de-monitoramento)
2. [Scripts de Backup](#scripts-de-backup)
3. [Scripts de Deploy](#scripts-de-deploy)
4. [Scripts de Manutenção](#scripts-de-manutenção)
5. [Configurações de Sistema](#configurações-de-sistema)

---

## 📊 Scripts de Monitoramento

### 1. **Monitor Principal** (`monitor-server.sh`)
```bash
#!/bin/bash
# monitor-server.sh - Monitoramento completo do servidor

# Configurações
LOG_FILE="/var/log/azerothcore-monitor.log"
ALERT_WEBHOOK="https://discord.com/api/webhooks/SEU_WEBHOOK"
DB_USER="acore"
DB_PASS="sua_senha"

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

log_message() {
    local level=$1
    local message=$2
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    case $level in
        "ERROR")   color=$RED ;;
        "SUCCESS") color=$GREEN ;;
        "WARNING") color=$YELLOW ;;
        *)         color=$NC ;;
    esac
    
    echo -e "${color}[$timestamp] [$level] $message${NC}"
    echo "[$timestamp] [$level] $message" >> $LOG_FILE
}

send_discord_alert() {
    local message=$1
    curl -H "Content-Type: application/json" \
         -X POST \
         -d "{\"content\":\"🚨 **ALERTA SERVIDOR AZEROTHCORE**\n$message\"}" \
         $ALERT_WEBHOOK 2>/dev/null
}

check_service() {
    local service=$1
    if systemctl is-active --quiet $service; then
        log_message "SUCCESS" "$service está rodando"
        return 0
    else
        log_message "ERROR" "$service está parado!"
        send_discord_alert "$service está parado no servidor!"
        return 1
    fi
}

check_port() {
    local port=$1
    local name=$2
    if netstat -tuln | grep -q ":$port "; then
        log_message "SUCCESS" "Porta $port ($name) está aberta"
        return 0
    else
        log_message "ERROR" "Porta $port ($name) não responde!"
        send_discord_alert "Porta $port ($name) não está respondendo!"
        return 1
    fi
}

check_mysql() {
    if mysql -u $DB_USER -p$DB_PASS -e "SELECT 1;" &>/dev/null; then
        log_message "SUCCESS" "MySQL conectando normalmente"
        
        # Verificar conexões ativas
        local connections=$(mysql -u $DB_USER -p$DB_PASS -e "SHOW STATUS LIKE 'Threads_connected';" | awk 'NR==2 {print $2}')
        log_message "INFO" "Conexões MySQL ativas: $connections"
        
        return 0
    else
        log_message "ERROR" "Falha na conexão MySQL!"
        send_discord_alert "MySQL não está respondendo!"
        return 1
    fi
}

check_resources() {
    # CPU
    local cpu_usage=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | sed 's/%us,//')
    log_message "INFO" "Uso de CPU: ${cpu_usage}%"
    
    # Memória
    local mem_usage=$(free | awk 'NR==2{printf "%.0f", $3*100/$2}')
    log_message "INFO" "Uso de memória: ${mem_usage}%"
    
    # Disco
    local disk_usage=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
    log_message "INFO" "Uso de disco: ${disk_usage}%"
    
    # Alertas
    if [ ${cpu_usage%.*} -gt 80 ]; then
        log_message "WARNING" "CPU alta: ${cpu_usage}%"
        send_discord_alert "CPU alta: ${cpu_usage}%"
    fi
    
    if [ $mem_usage -gt 85 ]; then
        log_message "WARNING" "Memória alta: ${mem_usage}%"
        send_discord_alert "Memória alta: ${mem_usage}%"
    fi
    
    if [ $disk_usage -gt 80 ]; then
        log_message "WARNING" "Disco cheio: ${disk_usage}%"
        send_discord_alert "Disco cheio: ${disk_usage}%"
    fi
}

check_players() {
    local online_players=$(mysql -u $DB_USER -p$DB_PASS -e "SELECT COUNT(*) FROM characters.characters WHERE online=1;" | tail -n1)
    log_message "INFO" "Jogadores online: $online_players"
    
    # Log de pico de jogadores
    local hour=$(date +%H)
    if [ $hour -eq 20 ]; then  # 20h = horário de pico
        echo "$(date '+%Y-%m-%d %H:%M:%S'),$online_players" >> /var/log/player-stats.csv
    fi
}

# Execução principal
log_message "INFO" "=== Iniciando monitoramento ==="

ERRORS=0

# Verificar serviços
check_service "mysql" || ((ERRORS++))
check_service "azerothcore-auth" || ((ERRORS++))
check_service "azerothcore-world" || ((ERRORS++))

# Verificar portas
check_port "3306" "MySQL" || ((ERRORS++))
check_port "3724" "AuthServer" || ((ERRORS++))
check_port "8085" "WorldServer" || ((ERRORS++))

# Verificar banco
check_mysql || ((ERRORS++))

# Verificar recursos
check_resources

# Verificar jogadores
check_players

# Resultado final
if [ $ERRORS -eq 0 ]; then
    log_message "SUCCESS" "Todos os sistemas funcionando normalmente"
else
    log_message "ERROR" "$ERRORS erro(s) detectado(s)!"
fi

log_message "INFO" "=== Monitoramento concluído ==="
```

### 2. **Monitor de Performance** (`performance-monitor.sh`)
```bash
#!/bin/bash
# performance-monitor.sh - Monitoramento detalhado de performance

STATS_FILE="/var/log/performance-stats.log"

collect_stats() {
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    # CPU
    local cpu_idle=$(top -bn1 | grep "Cpu(s)" | awk '{print $8}' | sed 's/%id,//')
    local cpu_usage=$((100 - ${cpu_idle%.*}))
    
    # Memória
    local mem_total=$(free -m | awk 'NR==2{print $2}')
    local mem_used=$(free -m | awk 'NR==2{print $3}')
    local mem_percent=$((mem_used * 100 / mem_total))
    
    # Disco I/O
    local disk_read=$(iostat -d 1 2 | tail -n +4 | awk '{sum+=$3} END {print sum}')
    local disk_write=$(iostat -d 1 2 | tail -n +4 | awk '{sum+=$4} END {print sum}')
    
    # MySQL
    local mysql_queries=$(mysql -u $DB_USER -p$DB_PASS -e "SHOW STATUS LIKE 'Queries';" | awk 'NR==2 {print $2}')
    local mysql_connections=$(mysql -u $DB_USER -p$DB_PASS -e "SHOW STATUS LIKE 'Threads_connected';" | awk 'NR==2 {print $2}')
    
    # Jogadores online
    local players_online=$(mysql -u $DB_USER -p$DB_PASS -e "SELECT COUNT(*) FROM characters.characters WHERE online=1;" | tail -n1)
    
    # Salvar estatísticas
    echo "$timestamp,$cpu_usage,$mem_percent,$disk_read,$disk_write,$mysql_queries,$mysql_connections,$players_online" >> $STATS_FILE
    
    # Manter apenas 30 dias de dados
    find /var/log/ -name "performance-stats.log" -mtime +30 -delete
}

# Executar coleta
collect_stats
```

---

## 💾 Scripts de Backup

### 1. **Backup Completo** (`backup-full.sh`)
```bash
#!/bin/bash
# backup-full.sh - Backup completo do servidor

BACKUP_BASE="/backup"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="$BACKUP_BASE/full_$DATE"
RETENTION_DAYS=30

# Configurações
DB_USER="root"
DB_PASS="sua_senha_root"
AZEROTHCORE_PATH="/opt/azerothcore"

# Criar diretório de backup
mkdir -p $BACKUP_DIR

log_backup() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a $BACKUP_DIR/backup.log
}

log_backup "🔄 Iniciando backup completo"

# 1. Backup do MySQL
log_backup "📊 Fazendo backup do banco de dados..."
mysqldump --single-transaction --routines --triggers --all-databases \
    -u $DB_USER -p$DB_PASS > $BACKUP_DIR/mysql_full.sql

if [ $? -eq 0 ]; then
    log_backup "✅ Backup do MySQL concluído"
    gzip $BACKUP_DIR/mysql_full.sql
else
    log_backup "❌ ERRO no backup do MySQL"
    exit 1
fi

# 2. Backup dos arquivos do AzerothCore
log_backup "📁 Fazendo backup dos arquivos..."
tar -czf $BACKUP_DIR/azerothcore_files.tar.gz \
    $AZEROTHCORE_PATH \
    --exclude=$AZEROTHCORE_PATH/logs/*.log \
    --exclude=$AZEROTHCORE_PATH/data/logs/*

if [ $? -eq 0 ]; then
    log_backup "✅ Backup dos arquivos concluído"
else
    log_backup "❌ ERRO no backup dos arquivos"
    exit 1
fi

# 3. Backup das configurações do sistema
log_backup "⚙️ Fazendo backup das configurações..."
mkdir -p $BACKUP_DIR/system_config
cp -r /etc/mysql/ $BACKUP_DIR/system_config/
cp /etc/systemd/system/azerothcore-* $BACKUP_DIR/system_config/ 2>/dev/null
cp /etc/ufw/user.rules $BACKUP_DIR/system_config/ 2>/dev/null

# 4. Informações do sistema
log_backup "📋 Coletando informações do sistema..."
cat > $BACKUP_DIR/system_info.txt << EOF
Backup criado em: $(date)
Hostname: $(hostname)
Kernel: $(uname -r)
Uptime: $(uptime)
Espaço em disco: $(df -h)
Memória: $(free -h)
Serviços ativos: $(systemctl list-units --type=service --state=active | grep azerothcore)
EOF

# 5. Verificar integridade
log_backup "🔍 Verificando integridade do backup..."
BACKUP_SIZE=$(du -sh $BACKUP_DIR | cut -f1)
FILE_COUNT=$(find $BACKUP_DIR -type f | wc -l)

log_backup "📊 Estatísticas do backup:"
log_backup "   • Tamanho total: $BACKUP_SIZE"
log_backup "   • Arquivos: $FILE_COUNT"
log_backup "   • Localização: $BACKUP_DIR"

# 6. Limpeza de backups antigos
log_backup "🧹 Limpando backups antigos (>$RETENTION_DAYS dias)..."
find $BACKUP_BASE -type d -name "full_*" -mtime +$RETENTION_DAYS -exec rm -rf {} \;

log_backup "✅ Backup completo finalizado com sucesso!"

# 7. Enviar notificação (opcional)
# curl -X POST -H 'Content-type: application/json' \
#     --data "{\"text\":\"✅ Backup completo realizado: $BACKUP_SIZE\"}" \
#     $SLACK_WEBHOOK_URL
```

### 2. **Backup Incremental** (`backup-incremental.sh`)
```bash
#!/bin/bash
# backup-incremental.sh - Backup incremental diário

BACKUP_BASE="/backup/incremental"
DATE=$(date +%Y%m%d)
BACKUP_DIR="$BACKUP_BASE/$DATE"
LAST_BACKUP_FILE="/tmp/last_backup_timestamp"

mkdir -p $BACKUP_DIR

# Determinar timestamp do último backup
if [ -f $LAST_BACKUP_FILE ]; then
    LAST_BACKUP=$(cat $LAST_BACKUP_FILE)
else
    LAST_BACKUP=$(date -d "yesterday" +%Y%m%d)
fi

echo "📅 Backup incremental desde: $LAST_BACKUP"

# 1. Backup incremental do MySQL (binlogs)
echo "📊 Backup incremental do MySQL..."
mysql -e "FLUSH LOGS;"
cp /var/log/mysql/mysql-bin.* $BACKUP_DIR/ 2>/dev/null

# 2. Arquivos modificados
echo "📁 Backup de arquivos modificados..."
find /opt/azerothcore -newer $LAST_BACKUP_FILE -type f \
    -exec cp --parents {} $BACKUP_DIR/ \; 2>/dev/null

# 3. Atualizar timestamp
date +%Y%m%d > $LAST_BACKUP_FILE

echo "✅ Backup incremental concluído: $BACKUP_DIR"
```

---

## 🚀 Scripts de Deploy

### 1. **Deploy Novo Servidor** (`deploy-server.sh`)
```bash
#!/bin/bash
# deploy-server.sh - Deploy automatizado de novo servidor

TEMPLATE_NAME="template-ct104"
NEW_CTID=$1
SERVER_NAME=$2
SERVER_TYPE=$3  # pvp, pve, rp

if [ $# -lt 3 ]; then
    echo "❌ Uso: $0 <CTID> <NOME> <TIPO>"
    echo "   Tipos: pvp, pve, rp"
    echo "   Exemplo: $0 105 'Servidor-PvP' pvp"
    exit 1
fi

echo "🚀 Criando servidor: $SERVER_NAME (ID: $NEW_CTID, Tipo: $SERVER_TYPE)"

# Configurações por tipo de servidor
case $SERVER_TYPE in
    "pvp")
        MEMORY=8192
        CORES=4
        REALM_TYPE=1  # PvP
        ;;
    "pve")
        MEMORY=6144
        CORES=3
        REALM_TYPE=0  # Normal
        ;;
    "rp")
        MEMORY=4096
        CORES=2
        REALM_TYPE=6  # RP
        ;;
    *)
        echo "❌ Tipo inválido: $SERVER_TYPE"
        exit 1
        ;;
esac

# 1. Verificar se CTID está disponível
if pct list | grep -q "^$NEW_CTID "; then
    echo "❌ ERRO: Container $NEW_CTID já existe!"
    exit 1
fi

# 2. Criar container do template
echo "📦 Criando container..."
pct create $NEW_CTID /var/lib/vz/template/cache/$TEMPLATE_NAME.tar.gz \
    --hostname $SERVER_NAME \
    --memory $MEMORY \
    --cores $CORES \
    --net0 name=eth0,bridge=vmbr0,ip=dhcp \
    --storage local-lvm \
    --unprivileged 1 \
    --start 1

if [ $? -ne 0 ]; then
    echo "❌ ERRO: Falha ao criar container!"
    exit 1
fi

echo "⏳ Aguardando inicialização..."
sleep 45

# 3. Configurar servidor
echo "⚙️ Configurando servidor..."
pct exec $NEW_CTID -- bash -c "
    # Atualizar hostname
    hostnamectl set-hostname $SERVER_NAME
    
    # Regenerar SSH keys
    rm -f /etc/ssh/ssh_host_*
    ssh-keygen -A
    
    # Configurar AzerothCore
    sed -i 's/RealmName = .*/RealmName = \"$SERVER_NAME\"/' /opt/azerothcore/etc/worldserver.conf
    sed -i 's/RealmZone = .*/RealmZone = $REALM_TYPE/' /opt/azerothcore/etc/worldserver.conf
    
    # Configurações específicas por tipo
    case '$SERVER_TYPE' in
        'pvp')
            sed -i 's/Rate.XP.Kill = .*/Rate.XP.Kill = 2/' /opt/azerothcore/etc/worldserver.conf
            sed -i 's/Rate.Honor = .*/Rate.Honor = 3/' /opt/azerothcore/etc/worldserver.conf
            ;;
        'pve')
            sed -i 's/Rate.XP.Kill = .*/Rate.XP.Kill = 1.5/' /opt/azerothcore/etc/worldserver.conf
            sed -i 's/Rate.Drop.Item.Normal = .*/Rate.Drop.Item.Normal = 2/' /opt/azerothcore/etc/worldserver.conf
            ;;
        'rp')
            sed -i 's/Rate.XP.Kill = .*/Rate.XP.Kill = 1/' /opt/azerothcore/etc/worldserver.conf
            sed -i 's/ChatStrictLinkChecking.Severity = .*/ChatStrictLinkChecking.Severity = 1/' /opt/azerothcore/etc/worldserver.conf
            ;;
    esac
    
    # Reiniciar serviços
    systemctl restart ssh
    systemctl restart azerothcore-auth
    systemctl restart azerothcore-world
"

# 4. Verificar se tudo está funcionando
echo "🔍 Verificando serviços..."
sleep 30

pct exec $NEW_CTID -- systemctl is-active azerothcore-auth
pct exec $NEW_CTID -- systemctl is-active azerothcore-world

if [ $? -eq 0 ]; then
    SERVER_IP=$(pct exec $NEW_CTID -- hostname -I | awk '{print $1}')
    
    echo ""
    echo "✅ Servidor criado com sucesso!"
    echo "📋 Detalhes:"
    echo "   • Container ID: $NEW_CTID"
    echo "   • Nome: $SERVER_NAME"
    echo "   • Tipo: $SERVER_TYPE"
    echo "   • IP: $SERVER_IP"
    echo "   • Memória: ${MEMORY}MB"
    echo "   • CPU Cores: $CORES"
    echo ""
    echo "🔗 Conexão:"
    echo "   • AuthServer: $SERVER_IP:3724"
    echo "   • WorldServer: $SERVER_IP:8085"
else
    echo "❌ ERRO: Serviços não iniciaram corretamente!"
    exit 1
fi
```

### 2. **Update Servidor** (`update-server.sh`)
```bash
#!/bin/bash
# update-server.sh - Atualização de servidor existente

CTID=$1
UPDATE_TYPE=$2  # core, database, config

if [ $# -lt 2 ]; then
    echo "❌ Uso: $0 <CTID> <TIPO>"
    echo "   Tipos: core, database, config"
    exit 1
fi

echo "🔄 Atualizando servidor $CTID ($UPDATE_TYPE)"

# Verificar se container existe
if ! pct list | grep -q "^$CTID "; then
    echo "❌ ERRO: Container $CTID não encontrado!"
    exit 1
fi

case $UPDATE_TYPE in
    "core")
        echo "🔄 Atualizando AzerothCore..."
        pct exec $CTID -- bash -c "
            cd /opt/azerothcore-source
            git pull origin master
            mkdir -p build && cd build
            cmake .. -DCMAKE_INSTALL_PREFIX=/opt/azerothcore
            make -j\$(nproc)
            make install
            systemctl restart azerothcore-auth azerothcore-world
        "
        ;;
    "database")
        echo "📊 Atualizando banco de dados..."
        pct exec $CTID -- bash -c "
            cd /opt/azerothcore-source/data/sql/updates
            # Aplicar updates pendentes
            find . -name '*.sql' -newer /tmp/last_db_update -exec mysql acore < {} \;
            touch /tmp/last_db_update
        "
        ;;
    "config")
        echo "⚙️ Atualizando configurações..."
        # Implementar lógica de atualização de configs
        ;;
esac

echo "✅ Atualização concluída!"
```

---

## 🔧 Scripts de Manutenção

### 1. **Manutenção Automática** (`maintenance.sh`)
```bash
#!/bin/bash
# maintenance.sh - Manutenção automática do servidor

MAINTENANCE_LOG="/var/log/maintenance.log"

log_maintenance() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a $MAINTENANCE_LOG
}

log_maintenance "🔧 Iniciando manutenção automática"

# 1. Otimizar banco de dados
log_maintenance "📊 Otimizando banco de dados..."
mysql -u root -p$MYSQL_ROOT_PASSWORD << EOF
USE auth;
OPTIMIZE TABLE account, account_access, account_banned;

USE characters;
OPTIMIZE TABLE characters, character_inventory, character_spell;

USE world;
OPTIMIZE TABLE creature, gameobject, quest_template, item_template;

-- Atualizar estatísticas
ANALYZE TABLE auth.account;
ANALYZE TABLE characters.characters;
ANALYZE TABLE world.creature;
EOF

# 2. Limpar logs antigos
log_maintenance "🧹 Limpando logs antigos..."
find /opt/azerothcore/logs/ -name "*.log" -mtime +7 -delete
find /var/log/ -name "*.log.*.gz" -mtime +30 -delete

# 3. Limpar cache
log_maintenance "💾 Limpando cache..."
rm -rf /tmp/azerothcore_*
apt-get clean

# 4. Verificar integridade do sistema de arquivos
log_maintenance "🔍 Verificando integridade..."
fsck -n / 2>/dev/null | grep -q "clean" && \
    log_maintenance "✅ Sistema de arquivos íntegro" || \
    log_maintenance "⚠️ Problemas detectados no sistema de arquivos"

# 5. Atualizar sistema (apenas patches de segurança)
log_maintenance "🔒 Aplicando patches de segurança..."
apt-get update -qq
apt-get upgrade -y --only-upgrade \
    $(apt list --upgradable 2>/dev/null | grep -i security | cut -d'/' -f1)

# 6. Reiniciar serviços se necessário
log_maintenance "🔄 Verificando necessidade de reinicialização..."
if [ -f /var/run/reboot-required ]; then
    log_maintenance "⚠️ Reinicialização necessária após atualizações"
    # Agendar reinicialização para horário de menor movimento
    echo "shutdown -r 05:00" | at now
fi

log_maintenance "✅ Manutenção automática concluída"
```

---

## ⚙️ Configurações de Sistema

### 1. **Systemd Services**

#### `/etc/systemd/system/azerothcore-auth.service`
```ini
[Unit]
Description=AzerothCore Authentication Server
After=mysql.service
Requires=mysql.service

[Service]
Type=simple
User=azerothcore
Group=azerothcore
WorkingDirectory=/opt/azerothcore/bin
ExecStart=/opt/azerothcore/bin/authserver
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
```

#### `/etc/systemd/system/azerothcore-world.service`
```ini
[Unit]
Description=AzerothCore World Server
After=azerothcore-auth.service mysql.service
Requires=azerothcore-auth.service mysql.service

[Service]
Type=simple
User=azerothcore
Group=azerothcore
WorkingDirectory=/opt/azerothcore/bin
ExecStart=/opt/azerothcore/bin/worldserver
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
```

### 2. **Crontab Completo**
```bash
# /etc/crontab - Tarefas automáticas

# Monitoramento a cada 5 minutos
*/5 * * * * root /opt/scripts/monitor-server.sh

# Backup incremental diário às 2:00
0 2 * * * root /opt/scripts/backup-incremental.sh

# Backup completo semanal aos domingos às 3:00
0 3 * * 0 root /opt/scripts/backup-full.sh

# Manutenção semanal às segundas às 4:00
0 4 * * 1 root /opt/scripts/maintenance.sh

# Coleta de estatísticas de performance a cada 15 minutos
*/15 * * * * root /opt/scripts/performance-monitor.sh

# Limpeza de logs diária às 1:00
0 1 * * * root find /opt/azerothcore/logs/ -name "*.log" -size +100M -delete

# Verificação de integridade do banco semanal
0 5 * * 2 root mysqlcheck --all-databases --auto-repair

# Restart semanal (opcional) - domingos às 6:00
0 6 * * 0 root /opt/scripts/restart-server.sh
```

### 3. **Configuração de Logs** (`/etc/rsyslog.d/azerothcore.conf`)
```
# AzerothCore logs
$template AzerothCoreFormat,"%timegenerated% %HOSTNAME% %syslogtag%%msg%\n"

# Auth Server
:programname, isequal, "authserver" /var/log/azerothcore/auth.log;AzerothCoreFormat
& stop

# World Server  
:programname, isequal, "worldserver" /var/log/azerothcore/world.log;AzerothCoreFormat
& stop

# Rotação de logs
$outchannel authlog,/var/log/azerothcore/auth.log,104857600,/opt/scripts/rotate-logs.sh
$outchannel worldlog,/var/log/azerothcore/world.log,104857600,/opt/scripts/rotate-logs.sh
```

---

**Scripts criados para automação completa do servidor AzerothCore!** 🚀

Todos os scripts estão prontos para uso e incluem:
- ✅ Monitoramento em tempo real
- ✅ Backups automáticos
- ✅ Deploy automatizado
- ✅ Manutenção programada
- ✅ Configurações de sistema otimizadas
