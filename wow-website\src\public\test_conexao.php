<?php
/**
 * Script de Teste de Conexão e Funcionalidades
 * 
 * <AUTHOR> Agent
 * @version 1.0
 */

require_once '../includes/config/config.php';

// Função para testar conexão com banco
function testDatabaseConnection() {
    $results = [];
    
    $databases = ['auth', 'characters', 'world'];
    
    foreach ($databases as $db_name) {
        try {
            $db = DatabaseManager::getConnection($db_name);
            $stmt = $db->query("SELECT 1");
            $results[$db_name] = [
                'status' => 'success',
                'message' => 'Conexão estabelecida com sucesso'
            ];
        } catch (Exception $e) {
            $results[$db_name] = [
                'status' => 'error',
                'message' => $e->getMessage()
            ];
        }
    }
    
    return $results;
}

// Função para testar estrutura das tabelas
function testTableStructure() {
    $results = [];
    
    try {
        // Testar tabela account
        $auth_db = DatabaseManager::getConnection('auth');
        $stmt = $auth_db->query("DESCRIBE account");
        $columns = $stmt->fetchAll();
        
        $required_columns = ['id', 'username', 'sha_pass_hash', 'email'];
        $found_columns = array_column($columns, 'Field');
        
        $missing = array_diff($required_columns, $found_columns);
        
        if (empty($missing)) {
            $results['account_table'] = [
                'status' => 'success',
                'message' => 'Tabela account possui todas as colunas necessárias'
            ];
        } else {
            $results['account_table'] = [
                'status' => 'error',
                'message' => 'Colunas faltando: ' . implode(', ', $missing)
            ];
        }
        
    } catch (Exception $e) {
        $results['account_table'] = [
            'status' => 'error',
            'message' => $e->getMessage()
        ];
    }
    
    try {
        // Testar tabela characters
        $char_db = DatabaseManager::getConnection('characters');
        $stmt = $char_db->query("DESCRIBE characters");
        $columns = $stmt->fetchAll();
        
        $required_columns = ['guid', 'account', 'name', 'level', 'race', 'class'];
        $found_columns = array_column($columns, 'Field');
        
        $missing = array_diff($required_columns, $found_columns);
        
        if (empty($missing)) {
            $results['characters_table'] = [
                'status' => 'success',
                'message' => 'Tabela characters possui todas as colunas necessárias'
            ];
        } else {
            $results['characters_table'] = [
                'status' => 'error',
                'message' => 'Colunas faltando: ' . implode(', ', $missing)
            ];
        }
        
    } catch (Exception $e) {
        $results['characters_table'] = [
            'status' => 'error',
            'message' => $e->getMessage()
        ];
    }
    
    return $results;
}

// Função para testar estatísticas do servidor
function testServerStats() {
    try {
        $stats = DatabaseManager::getServerStats();
        
        if (is_array($stats) && isset($stats['online'])) {
            return [
                'status' => 'success',
                'message' => 'Estatísticas obtidas com sucesso',
                'data' => $stats
            ];
        } else {
            return [
                'status' => 'error',
                'message' => 'Formato de estatísticas inválido'
            ];
        }
        
    } catch (Exception $e) {
        return [
            'status' => 'error',
            'message' => $e->getMessage()
        ];
    }
}

// Executar testes
$db_tests = testDatabaseConnection();
$table_tests = testTableStructure();
$stats_test = testServerStats();

?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste de Conexão - <?= SITE_NAME ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row">
            <div class="col-12">
                <h1 class="text-center mb-4">
                    <i class="fas fa-cog"></i> Teste de Conexão e Funcionalidades
                </h1>
                
                <!-- Teste de Conexão com Bancos -->
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-database"></i> Conexão com Bancos de Dados
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php foreach ($db_tests as $db_name => $result): ?>
                            <div class="row mb-2">
                                <div class="col-3">
                                    <strong><?= ucfirst($db_name) ?>:</strong>
                                </div>
                                <div class="col-9">
                                    <?php if ($result['status'] === 'success'): ?>
                                        <span class="badge bg-success">
                                            <i class="fas fa-check"></i> Conectado
                                        </span>
                                    <?php else: ?>
                                        <span class="badge bg-danger">
                                            <i class="fas fa-times"></i> Erro
                                        </span>
                                    <?php endif; ?>
                                    <small class="text-muted ms-2"><?= escape($result['message']) ?></small>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                
                <!-- Teste de Estrutura das Tabelas -->
                <div class="card mb-4">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-table"></i> Estrutura das Tabelas
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php foreach ($table_tests as $table_name => $result): ?>
                            <div class="row mb-2">
                                <div class="col-3">
                                    <strong><?= str_replace('_', ' ', ucfirst($table_name)) ?>:</strong>
                                </div>
                                <div class="col-9">
                                    <?php if ($result['status'] === 'success'): ?>
                                        <span class="badge bg-success">
                                            <i class="fas fa-check"></i> OK
                                        </span>
                                    <?php else: ?>
                                        <span class="badge bg-danger">
                                            <i class="fas fa-times"></i> Erro
                                        </span>
                                    <?php endif; ?>
                                    <small class="text-muted ms-2"><?= escape($result['message']) ?></small>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                
                <!-- Teste de Estatísticas -->
                <div class="card mb-4">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-bar"></i> Estatísticas do Servidor
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row mb-2">
                            <div class="col-3">
                                <strong>Status:</strong>
                            </div>
                            <div class="col-9">
                                <?php if ($stats_test['status'] === 'success'): ?>
                                    <span class="badge bg-success">
                                        <i class="fas fa-check"></i> Funcionando
                                    </span>
                                    
                                    <?php if (isset($stats_test['data'])): ?>
                                        <div class="mt-3">
                                            <h6>Dados obtidos:</h6>
                                            <ul class="list-unstyled ms-3">
                                                <li><strong>Servidor Online:</strong> 
                                                    <?= $stats_test['data']['online'] ? 'Sim' : 'Não' ?>
                                                </li>
                                                <li><strong>Jogadores Online:</strong> 
                                                    <?= $stats_test['data']['players_online'] ?>
                                                </li>
                                                <li><strong>Total de Contas:</strong> 
                                                    <?= $stats_test['data']['total_accounts'] ?>
                                                </li>
                                                <li><strong>Total de Personagens:</strong> 
                                                    <?= $stats_test['data']['total_characters'] ?>
                                                </li>
                                                <li><strong>Uptime:</strong> 
                                                    <?= $stats_test['data']['uptime'] ?>
                                                </li>
                                            </ul>
                                        </div>
                                    <?php endif; ?>
                                <?php else: ?>
                                    <span class="badge bg-danger">
                                        <i class="fas fa-times"></i> Erro
                                    </span>
                                    <small class="text-muted ms-2"><?= escape($stats_test['message']) ?></small>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Teste de Funções -->
                <div class="card mb-4">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-tools"></i> Funções do Sistema
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>Funções de Segurança:</h6>
                                <ul class="list-unstyled">
                                    <li>
                                        <i class="fas fa-check text-success"></i> 
                                        generateCSRFToken() - <?= function_exists('generateCSRFToken') ? 'OK' : 'ERRO' ?>
                                    </li>
                                    <li>
                                        <i class="fas fa-check text-success"></i> 
                                        escape() - <?= function_exists('escape') ? 'OK' : 'ERRO' ?>
                                    </li>
                                    <li>
                                        <i class="fas fa-check text-success"></i> 
                                        sanitizeInput() - <?= function_exists('sanitizeInput') ? 'OK' : 'ERRO' ?>
                                    </li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6>Funções de Helper:</h6>
                                <ul class="list-unstyled">
                                    <li>
                                        <i class="fas fa-check text-success"></i> 
                                        getClassName() - <?= function_exists('getClassName') ? 'OK' : 'ERRO' ?>
                                    </li>
                                    <li>
                                        <i class="fas fa-check text-success"></i> 
                                        getRaceName() - <?= function_exists('getRaceName') ? 'OK' : 'ERRO' ?>
                                    </li>
                                    <li>
                                        <i class="fas fa-check text-success"></i> 
                                        formatPlayTime() - <?= function_exists('formatPlayTime') ? 'OK' : 'ERRO' ?>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="text-center">
                    <a href="/" class="btn btn-primary">
                        <i class="fas fa-home"></i> Voltar ao Site
                    </a>
                    <a href="/register" class="btn btn-success">
                        <i class="fas fa-user-plus"></i> Testar Registro
                    </a>
                    <a href="/login" class="btn btn-info">
                        <i class="fas fa-sign-in-alt"></i> Testar Login
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
