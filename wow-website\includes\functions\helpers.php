<?php
/**
 * Funções Auxiliares
 * 
 * <AUTHOR> Agent
 * @version 1.0
 */

/**
 * Redirecionar para URL
 */
function redirect($url, $permanent = false) {
    if (!headers_sent()) {
        $code = $permanent ? 301 : 302;
        http_response_code($code);
        header("Location: $url");
    } else {
        echo "<script>window.location.href='$url';</script>";
    }
    exit;
}

/**
 * Formatar tempo para humano
 */
function timeAgo($datetime) {
    $time = time() - strtotime($datetime);
    
    if ($time < 60) return 'agora mesmo';
    if ($time < 3600) return floor($time/60) . ' minutos atrás';
    if ($time < 86400) return floor($time/3600) . ' horas atrás';
    if ($time < 2592000) return floor($time/86400) . ' dias atrás';
    if ($time < 31536000) return floor($time/2592000) . ' meses atrás';
    
    return floor($time/31536000) . ' anos atrás';
}

/**
 * Formatar bytes para humano
 */
function formatBytes($bytes, $precision = 2) {
    $units = ['B', 'KB', 'MB', 'GB', 'TB'];
    
    for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
        $bytes /= 1024;
    }
    
    return round($bytes, $precision) . ' ' . $units[$i];
}

/**
 * Gerar URL amigável
 */
function slugify($text) {
    $text = preg_replace('~[^\pL\d]+~u', '-', $text);
    $text = iconv('utf-8', 'us-ascii//TRANSLIT', $text);
    $text = preg_replace('~[^-\w]+~', '', $text);
    $text = trim($text, '-');
    $text = preg_replace('~-+~', '-', $text);
    $text = strtolower($text);
    
    return empty($text) ? 'n-a' : $text;
}

/**
 * Truncar texto
 */
function truncate($text, $length = 100, $suffix = '...') {
    if (strlen($text) <= $length) {
        return $text;
    }
    
    return substr($text, 0, $length) . $suffix;
}

/**
 * Obter avatar do usuário
 */
function getAvatar($email, $size = 80) {
    $hash = md5(strtolower(trim($email)));
    return "https://www.gravatar.com/avatar/$hash?s=$size&d=identicon";
}

/**
 * Formatar número
 */
function formatNumber($number) {
    if ($number >= 1000000) {
        return round($number / 1000000, 1) . 'M';
    } elseif ($number >= 1000) {
        return round($number / 1000, 1) . 'K';
    }
    return number_format($number);
}

/**
 * Obter classe CSS para nível de personagem
 */
function getClassColor($class_id) {
    $colors = [
        1 => '#C79C6E', // Warrior
        2 => '#F58CBA', // Paladin
        3 => '#ABD473', // Hunter
        4 => '#FFF569', // Rogue
        5 => '#FFFFFF', // Priest
        6 => '#C41F3B', // Death Knight
        7 => '#0070DE', // Shaman
        8 => '#69CCF0', // Mage
        9 => '#9482C9', // Warlock
        11 => '#FF7D0A' // Druid
    ];
    
    return $colors[$class_id] ?? '#FFFFFF';
}

/**
 * Obter nome da classe
 */
function getClassName($class_id) {
    $classes = [
        1 => 'Guerreiro',
        2 => 'Paladino',
        3 => 'Caçador',
        4 => 'Ladino',
        5 => 'Sacerdote',
        6 => 'Cavaleiro da Morte',
        7 => 'Xamã',
        8 => 'Mago',
        9 => 'Bruxo',
        11 => 'Druida'
    ];
    
    return $classes[$class_id] ?? 'Desconhecida';
}

/**
 * Obter nome da raça
 */
function getRaceName($race_id) {
    $races = [
        1 => 'Humano',
        2 => 'Orc',
        3 => 'Anão',
        4 => 'Elfo Noturno',
        5 => 'Morto-vivo',
        6 => 'Tauren',
        7 => 'Gnomo',
        8 => 'Troll',
        10 => 'Elfo Sangrento',
        11 => 'Draenei'
    ];
    
    return $races[$race_id] ?? 'Desconhecida';
}

/**
 * Verificar se é facção Aliança
 */
function isAlliance($race_id) {
    return in_array($race_id, [1, 3, 4, 7, 11]);
}

/**
 * Verificar se é facção Horda
 */
function isHorde($race_id) {
    return in_array($race_id, [2, 5, 6, 8, 10]);
}

/**
 * Obter ícone da facção
 */
function getFactionIcon($race_id) {
    return isAlliance($race_id) ? 'alliance' : 'horde';
}

/**
 * Gerar breadcrumb
 */
function generateBreadcrumb($items) {
    $html = '<nav aria-label="breadcrumb"><ol class="breadcrumb">';
    
    foreach ($items as $item) {
        if (isset($item['url'])) {
            $html .= '<li class="breadcrumb-item"><a href="' . $item['url'] . '">' . $item['title'] . '</a></li>';
        } else {
            $html .= '<li class="breadcrumb-item active" aria-current="page">' . $item['title'] . '</li>';
        }
    }
    
    $html .= '</ol></nav>';
    return $html;
}

/**
 * Gerar paginação
 */
function generatePagination($current_page, $total_pages, $base_url, $params = []) {
    if ($total_pages <= 1) return '';
    
    $html = '<nav aria-label="Paginação"><ul class="pagination justify-content-center">';
    
    // Botão anterior
    if ($current_page > 1) {
        $prev_url = $base_url . '?' . http_build_query(array_merge($params, ['page' => $current_page - 1]));
        $html .= '<li class="page-item"><a class="page-link" href="' . $prev_url . '">Anterior</a></li>';
    }
    
    // Páginas
    $start = max(1, $current_page - 2);
    $end = min($total_pages, $current_page + 2);
    
    if ($start > 1) {
        $first_url = $base_url . '?' . http_build_query(array_merge($params, ['page' => 1]));
        $html .= '<li class="page-item"><a class="page-link" href="' . $first_url . '">1</a></li>';
        if ($start > 2) {
            $html .= '<li class="page-item disabled"><span class="page-link">...</span></li>';
        }
    }
    
    for ($i = $start; $i <= $end; $i++) {
        if ($i == $current_page) {
            $html .= '<li class="page-item active"><span class="page-link">' . $i . '</span></li>';
        } else {
            $page_url = $base_url . '?' . http_build_query(array_merge($params, ['page' => $i]));
            $html .= '<li class="page-item"><a class="page-link" href="' . $page_url . '">' . $i . '</a></li>';
        }
    }
    
    if ($end < $total_pages) {
        if ($end < $total_pages - 1) {
            $html .= '<li class="page-item disabled"><span class="page-link">...</span></li>';
        }
        $last_url = $base_url . '?' . http_build_query(array_merge($params, ['page' => $total_pages]));
        $html .= '<li class="page-item"><a class="page-link" href="' . $last_url . '">' . $total_pages . '</a></li>';
    }
    
    // Botão próximo
    if ($current_page < $total_pages) {
        $next_url = $base_url . '?' . http_build_query(array_merge($params, ['page' => $current_page + 1]));
        $html .= '<li class="page-item"><a class="page-link" href="' . $next_url . '">Próximo</a></li>';
    }
    
    $html .= '</ul></nav>';
    return $html;
}

/**
 * Incluir template
 */
function includeTemplate($template, $vars = []) {
    extract($vars);
    $template_file = TEMPLATES_PATH . '/' . $template . '.php';
    
    if (file_exists($template_file)) {
        include $template_file;
    } else {
        writeLog('ERROR', "Template not found: $template");
        echo "Template não encontrado: $template";
    }
}

/**
 * Obter template como string
 */
function getTemplate($template, $vars = []) {
    ob_start();
    includeTemplate($template, $vars);
    return ob_get_clean();
}

/**
 * Enviar resposta JSON
 */
function jsonResponse($data, $status_code = 200) {
    http_response_code($status_code);
    header('Content-Type: application/json');
    echo json_encode($data, JSON_UNESCAPED_UNICODE);
    exit;
}

/**
 * Verificar se string é JSON válido
 */
function isValidJSON($string) {
    json_decode($string);
    return json_last_error() === JSON_ERROR_NONE;
}

/**
 * Gerar token único
 */
function generateToken($length = 32) {
    return bin2hex(random_bytes($length / 2));
}

/**
 * Verificar se arquivo é imagem
 */
function isValidImage($file) {
    $allowed_types = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    $file_info = finfo_open(FILEINFO_MIME_TYPE);
    $mime_type = finfo_file($file_info, $file);
    finfo_close($file_info);
    
    return in_array($mime_type, $allowed_types);
}

/**
 * Redimensionar imagem
 */
function resizeImage($source, $destination, $max_width, $max_height, $quality = 85) {
    list($orig_width, $orig_height, $type) = getimagesize($source);
    
    $ratio = min($max_width / $orig_width, $max_height / $orig_height);
    $new_width = $orig_width * $ratio;
    $new_height = $orig_height * $ratio;
    
    $new_image = imagecreatetruecolor($new_width, $new_height);
    
    switch ($type) {
        case IMAGETYPE_JPEG:
            $source_image = imagecreatefromjpeg($source);
            break;
        case IMAGETYPE_PNG:
            $source_image = imagecreatefrompng($source);
            imagealphablending($new_image, false);
            imagesavealpha($new_image, true);
            break;
        case IMAGETYPE_GIF:
            $source_image = imagecreatefromgif($source);
            break;
        default:
            return false;
    }
    
    imagecopyresampled($new_image, $source_image, 0, 0, 0, 0, $new_width, $new_height, $orig_width, $orig_height);
    
    switch ($type) {
        case IMAGETYPE_JPEG:
            imagejpeg($new_image, $destination, $quality);
            break;
        case IMAGETYPE_PNG:
            imagepng($new_image, $destination);
            break;
        case IMAGETYPE_GIF:
            imagegif($new_image, $destination);
            break;
    }
    
    imagedestroy($source_image);
    imagedestroy($new_image);
    
    return true;
}

/**
 * Enviar email
 */
function sendEmail($to, $subject, $message, $from = null) {
    $from = $from ?: SMTP_FROM;

    $headers = [
        'From' => $from,
        'Reply-To' => $from,
        'X-Mailer' => 'PHP/' . phpversion(),
        'MIME-Version' => '1.0',
        'Content-Type' => 'text/html; charset=UTF-8'
    ];

    $header_string = '';
    foreach ($headers as $key => $value) {
        $header_string .= "$key: $value\r\n";
    }

    return mail($to, $subject, $message, $header_string);
}

/**
 * Obter estatísticas do servidor em cache
 */
function getServerStatsCache() {
    $cache_key = 'server_stats';
    $stats = SimpleCache::get($cache_key);

    if ($stats === null) {
        $stats = DatabaseManager::getServerStats();
        SimpleCache::set($cache_key, $stats, 60); // Cache por 1 minuto
    }

    return $stats;
}

/**
 * Obter top players
 */
function getTopPlayers($limit = 10) {
    $cache_key = "top_players_$limit";
    $players = SimpleCache::get($cache_key);

    if ($players === null) {
        try {
            $char_db = DatabaseManager::getConnection('characters');
            $stmt = $char_db->prepare("
                SELECT name, level, race, class, totaltime
                FROM characters
                WHERE level > 1
                ORDER BY level DESC, totaltime DESC
                LIMIT ?
            ");
            $stmt->execute([$limit]);
            $players = $stmt->fetchAll();

            SimpleCache::set($cache_key, $players, 300); // Cache por 5 minutos
        } catch (Exception $e) {
            $players = [];
        }
    }

    return $players;
}

// Função getRealIP() já definida em security.php

/**
 * Formatar tempo de jogo
 */
function formatPlayTime($seconds) {
    if ($seconds < 3600) {
        return floor($seconds / 60) . 'm';
    } elseif ($seconds < 86400) {
        return floor($seconds / 3600) . 'h ' . floor(($seconds % 3600) / 60) . 'm';
    } else {
        $days = floor($seconds / 86400);
        $hours = floor(($seconds % 86400) / 3600);
        return $days . 'd ' . $hours . 'h';
    }
}

/**
 * Obter ícone da facção
 */
function getFactionIcon($race_id) {
    $alliance_races = [1, 3, 4, 7, 11]; // Human, Dwarf, Night Elf, Gnome, Draenei
    return in_array($race_id, $alliance_races) ? 'alliance' : 'horde';
}

?>
