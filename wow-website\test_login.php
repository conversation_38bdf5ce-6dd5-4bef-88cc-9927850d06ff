<?php
/**
 * <PERSON><PERSON> de <PERSON> - Debug
 * 
 * <AUTHOR> Agent
 * @version 1.0
 */

// Simular dados POST
$_POST = [
    'username' => 'cavalcrod20256',
    'password' => '123456', // Assumindo que foi a senha usada
    'csrf_token' => 'test_token'
];

$_SERVER['REQUEST_METHOD'] = 'POST';

echo "🧪 TESTE DE LOGIN DEBUG\n";
echo "======================\n";
echo "Username: " . $_POST['username'] . "\n";
echo "Password: " . $_POST['password'] . "\n\n";

// Incluir configurações
require_once 'includes/config/config.php';

try {
    $auth_db = DatabaseManager::getConnection('auth');
    echo "✅ Conexão com banco estabelecida\n";
    
    // Buscar conta
    $stmt = $auth_db->prepare("
        SELECT id, username, salt, verifier, email, expansion, locked 
        FROM account 
        WHERE username = ?
    ");
    $stmt->execute([$_POST['username']]);
    $account = $stmt->fetch();
    
    if ($account) {
        echo "✅ Conta encontrada no banco\n";
        echo "ID: " . $account['id'] . "\n";
        echo "Username: " . $account['username'] . "\n";
        echo "Email: " . $account['email'] . "\n";
        echo "Locked: " . ($account['locked'] ? 'SIM' : 'NÃO') . "\n";
        echo "Salt length: " . strlen($account['salt']) . " bytes\n";
        echo "Verifier length: " . strlen($account['verifier']) . " bytes\n\n";
        
        if ($account['locked']) {
            echo "❌ CONTA BLOQUEADA\n";
        } else {
            echo "🔐 Testando verificação SRP6...\n";
            
            // Função SRP6
            function calculateSRP6Verifier($username, $password, $salt) {
                $g = gmp_init(7);
                $N = gmp_init('894B645E89E1535BBDAD5B8B290650530801B18EBFBF5E8FAB3C82872A3E9BB7', 16);
                
                $username = strtoupper($username);
                $password = strtoupper($password);
                
                $h1 = sha1($username . ':' . $password, true);
                $h2 = sha1($salt . $h1, true);
                $h2_reversed = strrev($h2);
                $x = gmp_init('0x' . bin2hex($h2_reversed));
                $v = gmp_powm($g, $x, $N);
                
                $verifier_hex = gmp_strval($v, 16);
                if (strlen($verifier_hex) % 2 != 0) {
                    $verifier_hex = '0' . $verifier_hex;
                }
                
                $verifier = hex2bin($verifier_hex);
                $verifier = strrev($verifier);
                
                return str_pad($verifier, 32, "\0", STR_PAD_RIGHT);
            }
            
            $calculated_verifier = calculateSRP6Verifier($_POST['username'], $_POST['password'], $account['salt']);
            
            echo "Verifier calculado: " . bin2hex($calculated_verifier) . "\n";
            echo "Verifier do banco:  " . bin2hex($account['verifier']) . "\n";
            
            if (hash_equals($account['verifier'], $calculated_verifier)) {
                echo "✅ SENHA CORRETA! Login deveria funcionar.\n";
            } else {
                echo "❌ SENHA INCORRETA! Verifiers não coincidem.\n";
                
                // Testar com senhas diferentes
                $test_passwords = ['123456', 'senha123', 'password', ''];
                echo "\n🔍 Testando senhas comuns:\n";
                foreach ($test_passwords as $test_pass) {
                    $test_verifier = calculateSRP6Verifier($_POST['username'], $test_pass, $account['salt']);
                    if (hash_equals($account['verifier'], $test_verifier)) {
                        echo "✅ SENHA ENCONTRADA: '$test_pass'\n";
                        break;
                    } else {
                        echo "❌ Não é: '$test_pass'\n";
                    }
                }
            }
        }
    } else {
        echo "❌ Conta não encontrada no banco\n";
        
        // Listar contas similares
        $stmt = $auth_db->prepare("SELECT username FROM account WHERE username LIKE ? ORDER BY id DESC LIMIT 5");
        $stmt->execute(['%cavalcrod%']);
        $similar = $stmt->fetchAll();
        
        echo "Contas similares encontradas:\n";
        foreach ($similar as $acc) {
            echo "- " . $acc['username'] . "\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ ERRO: " . $e->getMessage() . "\n";
}

echo "\n======================\n";
echo "FIM DO TESTE\n";
?>
