<?php
/**
 * Debug de Sessão
 */

session_start();

echo "<h1>🔍 DEBUG DE SESSÃO</h1>";

echo "<h2>1. <PERSON><PERSON></h2>";
echo "<pre>";
print_r($_SESSION);
echo "</pre>";

echo "<h2>2. Verificações de Login</h2>";

// Verificar se está logado
$is_logged = isset($_SESSION['user_id']) && 
              isset($_SESSION['username']) &&
              isset($_SESSION['login_time']);

echo "✅ user_id: " . (isset($_SESSION['user_id']) ? $_SESSION['user_id'] : 'NÃO DEFINIDO') . "<br>";
echo "✅ username: " . (isset($_SESSION['username']) ? $_SESSION['username'] : 'NÃO DEFINIDO') . "<br>";
echo "✅ login_time: " . (isset($_SESSION['login_time']) ? $_SESSION['login_time'] : 'NÃO DEFINIDO') . "<br>";

if (isset($_SESSION['login_time'])) {
    $time_diff = time() - $_SESSION['login_time'];
    echo "⏰ Tempo desde login: {$time_diff} segundos<br>";
    echo "⏰ SESSION_TIMEOUT: 3600 segundos (1 hora)<br>";
    echo "⏰ Sessão válida: " . ($time_diff < 3600 ? "✅ SIM" : "❌ NÃO - EXPIROU") . "<br>";
}

echo "<h2>3. Teste de isLoggedIn()</h2>";

// Incluir config para testar função
require_once '/var/www/html/includes/config/config.php';

if (function_exists('isLoggedIn')) {
    $logged_in = isLoggedIn();
    echo "isLoggedIn() retorna: " . ($logged_in ? "✅ TRUE" : "❌ FALSE") . "<br>";
} else {
    echo "❌ Função isLoggedIn() não encontrada<br>";
}

echo "<h2>4. Informações do Sistema</h2>";
echo "Timestamp atual: " . time() . "<br>";
echo "Data atual: " . date('Y-m-d H:i:s') . "<br>";
echo "Session ID: " . session_id() . "<br>";

echo "<h2>5. Teste de Redirecionamento</h2>";
echo "<a href='/account.php' style='background: green; color: white; padding: 10px; text-decoration: none; border-radius: 5px;'>🚀 IR PARA CONTA</a><br><br>";
echo "<a href='/index.php' style='background: blue; color: white; padding: 10px; text-decoration: none; border-radius: 5px;'>🏠 PÁGINA INICIAL</a>";
?>
