# 🎮 Servidor AzerothCore - Guia Técnico Completo

## 📋 Índice
1. [<PERSON><PERSON><PERSON> Geral](#visão-geral)
2. [Arquitetura do Servidor](#arquitetura-do-servidor)
3. [Performance e Otimização](#performance-e-otimização)
4. [Seguran<PERSON>](#segurança)
5. [Backup e Recovery](#backup-e-recovery)
6. [Monitoramento](#monitoramento)
7. [Deploy e Automação](#deploy-e-automação)
8. [Manutenção](#manutenção)
9. [Troubleshooting](#troubleshooting)

---

## 🎯 Visão Geral

### Objetivo
Servidor World of Warcraft 3.3.5a baseado em AzerothCore com foco em:
- **Estabilidade** e alta disponibilidade
- **Performance** otimizada para múltiplos jogadores
- **Backup automatizado** e recovery rápido
- **Monitoramento** proativo
- **Deploy** automatizado via templates

### Tecnologias Utilizadas
- **Emulador**: AzerothCore (C++)
- **Banco de Dados**: MySQL 8.0+
- **Virtualização**: Proxmox VE (LXC Containers)
- **Sistema Operacional**: Ubuntu 22.04 LTS
- **Automação**: Bash Scripts
- **Monitoramento**: Scripts customizados + logs

---

## 🏗️ Arquitetura do Servidor

### Componentes Principais

#### 1. **WorldServer** (Servidor Principal)
```
Função: Gerencia o mundo do jogo, NPCs, quests, combate
Porta: 8085
CPU: 4+ cores dedicados
RAM: 8GB+ recomendado
```

#### 2. **AuthServer** (Servidor de Autenticação)
```
Função: Login, autenticação de contas
Porta: 3724
CPU: 2 cores
RAM: 2GB
```

#### 3. **MySQL Database**
```
Databases:
- auth: Contas de usuário
- characters: Personagens dos jogadores
- world: Dados do mundo (NPCs, quests, etc.)
```

### Estrutura de Diretórios
```
/opt/azerothcore/
├── bin/                    # Executáveis
├── etc/                    # Configurações
├── data/                   # Dados do cliente
├── logs/                   # Logs do servidor
├── sql/                    # Scripts SQL
└── backup/                 # Backups locais
```

---

## ⚡ Performance e Otimização

### 1. **Otimização do MySQL**

#### Configuração `/etc/mysql/mysql.conf.d/mysqld.cnf`:
```ini
[mysqld]
# Configurações de Performance
innodb_buffer_pool_size = 4G
innodb_log_file_size = 512M
innodb_flush_log_at_trx_commit = 2
innodb_flush_method = O_DIRECT

# Query Cache
query_cache_type = 1
query_cache_size = 256M
query_cache_limit = 2M

# Conexões
max_connections = 500
max_connect_errors = 1000000

# Timeouts
wait_timeout = 28800
interactive_timeout = 28800

# Logs
slow_query_log = 1
slow_query_log_file = /var/log/mysql/slow.log
long_query_time = 2
```

#### Scripts de Otimização:
```bash
# Otimizar tabelas semanalmente
#!/bin/bash
mysql -u root -p$MYSQL_ROOT_PASSWORD << EOF
USE world;
OPTIMIZE TABLE creature;
OPTIMIZE TABLE gameobject;
OPTIMIZE TABLE quest_template;
EOF
```

### 2. **Configuração do AzerothCore**

#### worldserver.conf (principais configurações):
```ini
# Performance
PlayerLimit = 100
MaxPingTime = 30000
SessionAddDelay = 10000

# Database
LoginDatabaseInfo = "127.0.0.1;3306;acore;password;auth"
WorldDatabaseInfo = "127.0.0.1;3306;acore;password;world"
CharacterDatabaseInfo = "127.0.0.1;3306;acore;password;characters"

# Logs
LogsDir = "/opt/azerothcore/logs"
LogLevel = 2
LogFile = "Server.log"
LogTimestamp = 1

# Anti-Cheat
Warden.Enabled = 1
AntiCheat.Enable = 1
```

### 3. **Otimização do Sistema**

#### Configurações do Kernel:
```bash
# /etc/sysctl.conf
net.core.rmem_max = 16777216
net.core.wmem_max = 16777216
net.ipv4.tcp_rmem = 4096 87380 16777216
net.ipv4.tcp_wmem = 4096 65536 16777216
vm.swappiness = 10
```

---

## 🛡️ Segurança

### 1. **Firewall (UFW)**
```bash
# Configuração básica
ufw default deny incoming
ufw default allow outgoing

# Portas do AzerothCore
ufw allow 3724/tcp  # AuthServer
ufw allow 8085/tcp  # WorldServer

# SSH (apenas IPs específicos)
ufw allow from ***********/24 to any port 22

# MySQL (apenas local)
ufw deny 3306/tcp

ufw enable
```

### 2. **Fail2Ban**
```ini
# /etc/fail2ban/jail.local
[sshd]
enabled = true
port = ssh
filter = sshd
logpath = /var/log/auth.log
maxretry = 3
bantime = 3600

[azerothcore-auth]
enabled = true
port = 3724
filter = azerothcore-auth
logpath = /opt/azerothcore/logs/Auth.log
maxretry = 5
bantime = 1800
```

### 3. **Backup de Segurança**
```bash
# Script de backup diário
#!/bin/bash
BACKUP_DIR="/backup/$(date +%Y%m%d)"
mkdir -p $BACKUP_DIR

# Backup do banco
mysqldump --all-databases > $BACKUP_DIR/mysql_backup.sql

# Backup dos dados
tar -czf $BACKUP_DIR/azerothcore_data.tar.gz /opt/azerothcore/

# Manter apenas 7 dias
find /backup/ -type d -mtime +7 -exec rm -rf {} \;
```

---

## 💾 Backup e Recovery

### 1. **Estratégia de Backup**

#### Tipos de Backup:
- **Diário**: Banco de dados completo
- **Semanal**: Sistema completo (template)
- **Mensal**: Backup offsite
- **Tempo Real**: Logs de transações

#### Script de Backup Automatizado:
```bash
#!/bin/bash
# backup-azerothcore.sh

BACKUP_BASE="/backup"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="$BACKUP_BASE/$DATE"

mkdir -p $BACKUP_DIR

echo "🔄 Iniciando backup - $DATE"

# 1. Backup do MySQL
echo "📊 Backup do banco de dados..."
mysqldump --single-transaction --routines --triggers \
  --all-databases > $BACKUP_DIR/mysql_full.sql

# 2. Backup dos arquivos
echo "📁 Backup dos arquivos..."
tar -czf $BACKUP_DIR/azerothcore_files.tar.gz \
  /opt/azerothcore/ \
  --exclude=/opt/azerothcore/logs/*.log

# 3. Backup das configurações
echo "⚙️ Backup das configurações..."
cp -r /etc/mysql/ $BACKUP_DIR/mysql_config/
cp /opt/azerothcore/etc/*.conf $BACKUP_DIR/

# 4. Verificação
echo "✅ Verificando integridade..."
if [ -f "$BACKUP_DIR/mysql_full.sql" ] && [ -f "$BACKUP_DIR/azerothcore_files.tar.gz" ]; then
  echo "✅ Backup concluído: $BACKUP_DIR"
  
  # Limpar backups antigos (manter 30 dias)
  find $BACKUP_BASE -type d -mtime +30 -exec rm -rf {} \;
else
  echo "❌ ERRO: Backup falhou!"
  exit 1
fi
```

### 2. **Recovery Procedures**

#### Recovery Completo:
```bash
#!/bin/bash
# recovery-azerothcore.sh

BACKUP_DIR=$1
if [ -z "$BACKUP_DIR" ]; then
  echo "Uso: $0 /backup/20241221_143000"
  exit 1
fi

echo "🔄 Iniciando recovery de $BACKUP_DIR"

# 1. Parar serviços
systemctl stop azerothcore-world
systemctl stop azerothcore-auth
systemctl stop mysql

# 2. Restaurar banco
echo "📊 Restaurando banco de dados..."
mysql < $BACKUP_DIR/mysql_full.sql

# 3. Restaurar arquivos
echo "📁 Restaurando arquivos..."
rm -rf /opt/azerothcore/
tar -xzf $BACKUP_DIR/azerothcore_files.tar.gz -C /

# 4. Restaurar configurações
echo "⚙️ Restaurando configurações..."
cp -r $BACKUP_DIR/mysql_config/* /etc/mysql/

# 5. Reiniciar serviços
systemctl start mysql
systemctl start azerothcore-auth
systemctl start azerothcore-world

echo "✅ Recovery concluído!"
```

---

## 📊 Monitoramento

### 1. **Script de Monitoramento Principal**
```bash
#!/bin/bash
# monitor-azerothcore.sh

LOG_FILE="/var/log/azerothcore-monitor.log"
ALERT_EMAIL="<EMAIL>"

log_message() {
  echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a $LOG_FILE
}

check_service() {
  local service=$1
  if systemctl is-active --quiet $service; then
    log_message "✅ $service está rodando"
    return 0
  else
    log_message "❌ $service está parado!"
    return 1
  fi
}

check_port() {
  local port=$1
  local name=$2
  if netstat -tuln | grep -q ":$port "; then
    log_message "✅ Porta $port ($name) está aberta"
    return 0
  else
    log_message "❌ Porta $port ($name) não está respondendo!"
    return 1
  fi
}

check_mysql_connection() {
  if mysql -u acore -p$DB_PASSWORD -e "SELECT 1;" &>/dev/null; then
    log_message "✅ Conexão MySQL OK"
    return 0
  else
    log_message "❌ Falha na conexão MySQL!"
    return 1
  fi
}

check_disk_space() {
  local usage=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
  if [ $usage -lt 80 ]; then
    log_message "✅ Espaço em disco: ${usage}%"
    return 0
  else
    log_message "⚠️ ALERTA: Espaço em disco: ${usage}%"
    return 1
  fi
}

check_memory() {
  local mem_usage=$(free | awk 'NR==2{printf "%.0f", $3*100/$2}')
  if [ $mem_usage -lt 90 ]; then
    log_message "✅ Uso de memória: ${mem_usage}%"
    return 0
  else
    log_message "⚠️ ALERTA: Uso de memória: ${mem_usage}%"
    return 1
  fi
}

# Executar verificações
log_message "🔍 Iniciando monitoramento..."

ERRORS=0

check_service "mysql" || ((ERRORS++))
check_service "azerothcore-auth" || ((ERRORS++))
check_service "azerothcore-world" || ((ERRORS++))

check_port "3306" "MySQL" || ((ERRORS++))
check_port "3724" "AuthServer" || ((ERRORS++))
check_port "8085" "WorldServer" || ((ERRORS++))

check_mysql_connection || ((ERRORS++))
check_disk_space || ((ERRORS++))
check_memory || ((ERRORS++))

if [ $ERRORS -gt 0 ]; then
  log_message "❌ $ERRORS erro(s) encontrado(s)!"
  # Enviar alerta por email (configurar sendmail/postfix)
  # echo "Erros detectados no servidor AzerothCore" | mail -s "ALERTA Servidor" $ALERT_EMAIL
else
  log_message "✅ Todos os sistemas funcionando normalmente"
fi

log_message "🏁 Monitoramento concluído"
```

### 2. **Crontab para Automação**
```bash
# Editar crontab: crontab -e

# Monitoramento a cada 5 minutos
*/5 * * * * /opt/scripts/monitor-azerothcore.sh

# Backup diário às 3:00
0 3 * * * /opt/scripts/backup-azerothcore.sh

# Limpeza de logs semanalmente
0 2 * * 0 find /opt/azerothcore/logs/ -name "*.log" -mtime +7 -delete

# Otimização do MySQL semanalmente
0 4 * * 0 /opt/scripts/optimize-mysql.sh

# Reinicialização semanal (opcional)
0 5 * * 0 /opt/scripts/restart-server.sh
```

---

## 🚀 Deploy e Automação

### 1. **Template System**
O script `gerar-template-lxc.sh` que criamos automatiza:
- ✅ Backup do container
- ✅ Limpeza de configurações
- ✅ Criação de template limpo
- ✅ Verificações de segurança

### 2. **Deploy Automatizado**
```bash
#!/bin/bash
# deploy-novo-servidor.sh

TEMPLATE_NAME="template-ct104"
NEW_CTID=$1
SERVER_NAME=$2

if [ -z "$NEW_CTID" ] || [ -z "$SERVER_NAME" ]; then
  echo "Uso: $0 <CTID> <NOME_SERVIDOR>"
  echo "Exemplo: $0 105 'Servidor-PvP'"
  exit 1
fi

echo "🚀 Criando novo servidor: $SERVER_NAME (ID: $NEW_CTID)"

# 1. Criar container do template
pct create $NEW_CTID /var/lib/vz/template/cache/$TEMPLATE_NAME.tar.gz \
  --hostname $SERVER_NAME \
  --memory 8192 \
  --cores 4 \
  --net0 name=eth0,bridge=vmbr0,ip=dhcp \
  --storage local-lvm \
  --unprivileged 1

# 2. Iniciar container
pct start $NEW_CTID

# 3. Configurar rede (aguardar boot)
sleep 30

# 4. Configurar servidor específico
pct exec $NEW_CTID -- bash -c "
  # Atualizar hostname
  hostnamectl set-hostname $SERVER_NAME
  
  # Regenerar SSH keys
  rm -f /etc/ssh/ssh_host_*
  ssh-keygen -A
  
  # Configurar AzerothCore
  sed -i 's/RealmName = .*/RealmName = \"$SERVER_NAME\"/' /opt/azerothcore/etc/worldserver.conf
  
  # Reiniciar serviços
  systemctl restart ssh
  systemctl restart azerothcore-auth
  systemctl restart azerothcore-world
"

echo "✅ Servidor $SERVER_NAME criado com sucesso!"
echo "📋 Detalhes:"
echo "   • Container ID: $NEW_CTID"
echo "   • Nome: $SERVER_NAME"
echo "   • IP: $(pct exec $NEW_CTID -- hostname -I)"
```

---

## 🔧 Manutenção

### 1. **Rotinas de Manutenção**

#### Diária:
- Verificação de logs de erro
- Backup automático
- Monitoramento de performance

#### Semanal:
- Otimização do banco de dados
- Limpeza de logs antigos
- Verificação de segurança
- Teste de recovery

#### Mensal:
- Atualização do sistema
- Análise de performance
- Backup offsite
- Revisão de configurações

### 2. **Scripts de Manutenção**
```bash
#!/bin/bash
# manutencao-semanal.sh

echo "🔧 Iniciando manutenção semanal..."

# 1. Otimizar banco
echo "📊 Otimizando banco de dados..."
mysql -u root -p$MYSQL_ROOT_PASSWORD << EOF
OPTIMIZE TABLE auth.account;
OPTIMIZE TABLE characters.characters;
OPTIMIZE TABLE world.creature;
OPTIMIZE TABLE world.gameobject;
EOF

# 2. Limpar logs
echo "🧹 Limpando logs antigos..."
find /opt/azerothcore/logs/ -name "*.log" -mtime +7 -delete
find /var/log/ -name "*.log.*.gz" -mtime +30 -delete

# 3. Verificar integridade
echo "🔍 Verificando integridade..."
mysqlcheck --all-databases --auto-repair

# 4. Atualizar estatísticas
echo "📈 Atualizando estatísticas..."
mysql -u root -p$MYSQL_ROOT_PASSWORD << EOF
ANALYZE TABLE auth.account;
ANALYZE TABLE characters.characters;
ANALYZE TABLE world.creature;
EOF

echo "✅ Manutenção semanal concluída!"
```

---

## 🚨 Troubleshooting

### Problemas Comuns

#### 1. **Servidor não inicia**
```bash
# Verificar logs
tail -f /opt/azerothcore/logs/Server.log

# Verificar configuração
/opt/azerothcore/bin/worldserver --dry-run

# Verificar banco
mysql -u acore -p -e "SHOW DATABASES;"
```

#### 2. **Performance baixa**
```bash
# Verificar CPU/Memória
htop

# Verificar queries lentas
tail -f /var/log/mysql/slow.log

# Verificar conexões
mysql -e "SHOW PROCESSLIST;"
```

#### 3. **Problemas de conexão**
```bash
# Verificar portas
netstat -tuln | grep -E "(3724|8085)"

# Verificar firewall
ufw status

# Testar conectividade
telnet localhost 3724
telnet localhost 8085
```

### Logs Importantes
```
/opt/azerothcore/logs/Server.log     # Log principal do WorldServer
/opt/azerothcore/logs/Auth.log       # Log do AuthServer
/var/log/mysql/error.log             # Erros do MySQL
/var/log/mysql/slow.log              # Queries lentas
/var/log/syslog                      # Log do sistema
```

---

## 📞 Contatos e Suporte

### Recursos Úteis
- **AzerothCore Wiki**: https://www.azerothcore.org/wiki/
- **Discord AzerothCore**: https://discord.gg/gkt4y2x
- **GitHub**: https://github.com/azerothcore/azerothcore-wotlk

### Comandos de Emergência
```bash
# Parar tudo
systemctl stop azerothcore-world azerothcore-auth mysql

# Reiniciar tudo
systemctl start mysql azerothcore-auth azerothcore-world

# Backup de emergência
mysqldump --all-databases > /tmp/emergency_backup.sql

# Recovery rápido
mysql < /backup/latest/mysql_full.sql
```

---

**Documento criado em**: $(date)
**Versão**: 1.0
**Autor**: Augment Agent
**Servidor**: AzerothCore WotLK 3.3.5a
