<?php
/**
 * Debug Login - Teste Simples
 */

echo "<h1>🧪 DEBUG LOGIN</h1>";

// Incluir configurações
require_once 'src/includes/config/config.php';

echo "<h2>1. Teste de Conexão</h2>";
try {
    $auth_db = DatabaseManager::getConnection('auth');
    echo "✅ Conexão com banco estabelecida<br>";
} catch (Exception $e) {
    echo "❌ Erro de conexão: " . $e->getMessage() . "<br>";
    exit;
}

echo "<h2>2. Buscar Conta</h2>";
$username = 'cavalcrod20256';
$stmt = $auth_db->prepare("SELECT id, username, salt, verifier, email, expansion, locked FROM account WHERE username = ?");
$stmt->execute([$username]);
$account = $stmt->fetch();

if ($account) {
    echo "✅ Conta encontrada<br>";
    echo "ID: " . $account['id'] . "<br>";
    echo "Username: " . $account['username'] . "<br>";
    echo "Email: " . $account['email'] . "<br>";
    echo "Locked: " . ($account['locked'] ? 'SIM' : 'NÃO') . "<br>";
} else {
    echo "❌ Conta não encontrada<br>";
    exit;
}

echo "<h2>3. Teste de Senhas</h2>";

// Função SRP6
function calculateSRP6Verifier($username, $password, $salt) {
    $g = gmp_init(7);
    $N = gmp_init('894B645E89E1535BBDAD5B8B290650530801B18EBFBF5E8FAB3C82872A3E9BB7', 16);
    
    $username = strtoupper($username);
    $password = strtoupper($password);
    
    $h1 = sha1($username . ':' . $password, true);
    $h2 = sha1($salt . $h1, true);
    $h2_reversed = strrev($h2);
    $x = gmp_init('0x' . bin2hex($h2_reversed));
    $v = gmp_powm($g, $x, $N);
    
    $verifier_hex = gmp_strval($v, 16);
    if (strlen($verifier_hex) % 2 != 0) {
        $verifier_hex = '0' . $verifier_hex;
    }
    
    $verifier = hex2bin($verifier_hex);
    $verifier = strrev($verifier);
    
    return str_pad($verifier, 32, "\0", STR_PAD_RIGHT);
}

// Testar senhas comuns
$test_passwords = ['123456', 'senha123', 'password', '12345', 'admin', 'cavalcrod', 'cavalcrod20256', ''];

foreach ($test_passwords as $test_pass) {
    $calculated_verifier = calculateSRP6Verifier($username, $test_pass, $account['salt']);
    
    if (hash_equals($account['verifier'], $calculated_verifier)) {
        echo "✅ <strong style='color: green;'>SENHA ENCONTRADA: '$test_pass'</strong><br>";
        
        // Testar login completo
        echo "<h2>4. Teste de Login Completo</h2>";
        session_start();
        $_SESSION['user_id'] = $account['id'];
        $_SESSION['username'] = $account['username'];
        $_SESSION['email'] = $account['email'];
        $_SESSION['user_level'] = $account['expansion'] ?? 0;
        $_SESSION['login_time'] = time();
        
        echo "✅ Sessão criada com sucesso<br>";
        echo "User ID: " . $_SESSION['user_id'] . "<br>";
        echo "Username: " . $_SESSION['username'] . "<br>";
        
        // Testar função isLoggedIn
        if (function_exists('isLoggedIn')) {
            if (isLoggedIn()) {
                echo "✅ isLoggedIn() retorna TRUE<br>";
            } else {
                echo "❌ isLoggedIn() retorna FALSE<br>";
            }
        }
        
        echo "<h2>5. Teste de Redirecionamento</h2>";
        echo "<a href='/account' style='background: green; color: white; padding: 10px; text-decoration: none; border-radius: 5px;'>🚀 IR PARA CONTA</a><br><br>";
        echo "<a href='/login' style='background: blue; color: white; padding: 10px; text-decoration: none; border-radius: 5px;'>🔄 VOLTAR AO LOGIN</a>";
        
        break;
    } else {
        echo "❌ Não é: '$test_pass'<br>";
    }
}

echo "<h2>6. Informações de Debug</h2>";
echo "Salt (hex): " . bin2hex($account['salt']) . "<br>";
echo "Verifier (hex): " . bin2hex($account['verifier']) . "<br>";
echo "Session ID: " . session_id() . "<br>";
echo "PHP Version: " . phpversion() . "<br>";

?>
