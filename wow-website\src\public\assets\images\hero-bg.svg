<svg xmlns="http://www.w3.org/2000/svg" width="1920" height="1080" viewBox="0 0 1920 1080">
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1a1a2e;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#16213e;stop-opacity:1" />
    </linearGradient>
    <filter id="noise" x="0%" y="0%" width="100%" height="100%">
      <feTurbulence type="fractalNoise" baseFrequency="0.01" numOctaves="3" result="noise" />
      <feDisplacementMap in="SourceGraphic" in2="noise" scale="5" xChannelSelector="R" yChannelSelector="G" />
    </filter>
  </defs>
  <rect width="1920" height="1080" fill="url(#grad1)" />
  <g filter="url(#noise)">
    <path d="M0,1080 L1920,1080 L1920,800 C1600,900 1200,850 800,950 C400,1050 200,1000 0,900 Z" fill="#0f172a" opacity="0.7" />
    <path d="M0,1080 L1920,1080 L1920,900 C1700,950 1400,920 1100,980 C800,1040 400,1020 0,950 Z" fill="#1e293b" opacity="0.5" />
  </g>
  <g opacity="0.3">
    <circle cx="300" cy="300" r="100" fill="#f4c430" opacity="0.1" />
    <circle cx="1600" cy="200" r="150" fill="#f4c430" opacity="0.1" />
    <circle cx="1000" cy="500" r="200" fill="#f4c430" opacity="0.05" />
  </g>
</svg>