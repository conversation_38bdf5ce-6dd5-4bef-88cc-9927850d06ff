/**
 * JavaScript Principal do Site WoW
 * 
 * <AUTHOR> Agent
 * @version 1.0
 */

// ===== CONFIGURAÇÕES GLOBAIS =====
const WoWSite = {
    config: {
        apiUrl: '/api',
        refreshInterval: 30000, // 30 segundos
        animationDuration: 300
    },
    
    // Cache simples
    cache: new Map(),
    
    // Utilitários
    utils: {
        // Fazer requisição AJAX
        async request(url, options = {}) {
            try {
                const response = await fetch(url, {
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest',
                        ...options.headers
                    },
                    ...options
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                return await response.json();
            } catch (error) {
                console.error('Erro na requisição:', error);
                throw error;
            }
        },
        
        // Mostrar notificação
        showNotification(message, type = 'info', duration = 5000) {
            const notification = document.createElement('div');
            notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            
            notification.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            document.body.appendChild(notification);
            
            // Auto-remover após duração especificada
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, duration);
        },
        
        // Formatar número
        formatNumber(num) {
            if (num >= 1000000) {
                return (num / 1000000).toFixed(1) + 'M';
            } else if (num >= 1000) {
                return (num / 1000).toFixed(1) + 'K';
            }
            return num.toLocaleString();
        },
        
        // Debounce function
        debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        },
        
        // Validar email
        isValidEmail(email) {
            const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return re.test(email);
        },
        
        // Copiar para clipboard
        async copyToClipboard(text) {
            try {
                await navigator.clipboard.writeText(text);
                this.showNotification('Copiado para a área de transferência!', 'success', 2000);
            } catch (err) {
                console.error('Erro ao copiar:', err);
                this.showNotification('Erro ao copiar texto', 'danger');
            }
        }
    }
};

// ===== INICIALIZAÇÃO =====
document.addEventListener('DOMContentLoaded', function() {
    console.log('🎮 WoW Site carregado!');
    
    // Inicializar componentes
    WoWSite.init();
});

// ===== MÉTODOS PRINCIPAIS =====
WoWSite.init = function() {
    this.initNavbar();
    this.initServerStats();
    this.initAnimations();
    this.initTooltips();
    this.initForms();
    this.initLazyLoading();
    
    console.log('✅ Todos os componentes inicializados');
};

// ===== NAVBAR =====
WoWSite.initNavbar = function() {
    const navbar = document.querySelector('.navbar');
    
    // Efeito de scroll na navbar
    window.addEventListener('scroll', () => {
        if (window.scrollY > 50) {
            navbar.classList.add('navbar-scrolled');
        } else {
            navbar.classList.remove('navbar-scrolled');
        }
    });
    
    // Fechar menu mobile ao clicar em link
    document.querySelectorAll('.navbar-nav .nav-link').forEach(link => {
        link.addEventListener('click', () => {
            const navbarCollapse = document.querySelector('.navbar-collapse');
            if (navbarCollapse.classList.contains('show')) {
                const bsCollapse = new bootstrap.Collapse(navbarCollapse);
                bsCollapse.hide();
            }
        });
    });
};

// ===== ESTATÍSTICAS DO SERVIDOR =====
WoWSite.initServerStats = function() {
    this.updateServerStats();
    
    // Atualizar estatísticas periodicamente
    setInterval(() => {
        this.updateServerStats();
    }, this.config.refreshInterval);
};

WoWSite.updateServerStats = async function() {
    try {
        const stats = await this.utils.request(`${this.config.apiUrl}/server-stats.php`);
        
        // Atualizar elementos na página
        const elements = {
            playersOnline: document.querySelector('[data-stat="players-online"]'),
            totalAccounts: document.querySelector('[data-stat="total-accounts"]'),
            serverStatus: document.querySelector('[data-stat="server-status"]'),
            uptime: document.querySelector('[data-stat="uptime"]')
        };
        
        if (elements.playersOnline) {
            this.animateNumber(elements.playersOnline, stats.players_online);
        }
        
        if (elements.totalAccounts) {
            this.animateNumber(elements.totalAccounts, stats.total_accounts);
        }
        
        if (elements.serverStatus) {
            elements.serverStatus.innerHTML = stats.online 
                ? '<i class="fas fa-circle text-success"></i> ONLINE'
                : '<i class="fas fa-circle text-danger"></i> OFFLINE';
        }
        
        if (elements.uptime) {
            elements.uptime.textContent = stats.uptime;
        }
        
        // Cache dos dados
        this.cache.set('server-stats', stats);
        
    } catch (error) {
        console.error('Erro ao atualizar estatísticas:', error);
    }
};

// ===== ANIMAÇÕES =====
WoWSite.initAnimations = function() {
    // Intersection Observer para animações de entrada
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');
                observer.unobserve(entry.target);
            }
        });
    }, observerOptions);
    
    // Observar elementos com classe animate-on-scroll
    document.querySelectorAll('.animate-on-scroll').forEach(el => {
        observer.observe(el);
    });
};

WoWSite.animateNumber = function(element, targetNumber) {
    const currentNumber = parseInt(element.textContent.replace(/[^\d]/g, '')) || 0;
    const increment = (targetNumber - currentNumber) / 20;
    let current = currentNumber;
    
    const timer = setInterval(() => {
        current += increment;
        if ((increment > 0 && current >= targetNumber) || 
            (increment < 0 && current <= targetNumber)) {
            current = targetNumber;
            clearInterval(timer);
        }
        element.textContent = this.utils.formatNumber(Math.floor(current));
    }, 50);
};

// ===== TOOLTIPS =====
WoWSite.initTooltips = function() {
    // Inicializar tooltips do Bootstrap
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
};

// ===== FORMULÁRIOS =====
WoWSite.initForms = function() {
    // Validação em tempo real
    document.querySelectorAll('form[data-validate]').forEach(form => {
        const inputs = form.querySelectorAll('input, textarea, select');
        
        inputs.forEach(input => {
            input.addEventListener('blur', () => {
                this.validateField(input);
            });
            
            input.addEventListener('input', this.utils.debounce(() => {
                this.validateField(input);
            }, 500));
        });
        
        // Submissão do formulário
        form.addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleFormSubmit(form);
        });
    });
};

WoWSite.validateField = function(field) {
    const value = field.value.trim();
    const rules = field.dataset;
    let isValid = true;
    let message = '';
    
    // Required
    if (rules.required && !value) {
        isValid = false;
        message = 'Este campo é obrigatório';
    }
    
    // Email
    if (rules.email && value && !this.utils.isValidEmail(value)) {
        isValid = false;
        message = 'Email inválido';
    }
    
    // Min length
    if (rules.minLength && value.length < parseInt(rules.minLength)) {
        isValid = false;
        message = `Mínimo ${rules.minLength} caracteres`;
    }
    
    // Max length
    if (rules.maxLength && value.length > parseInt(rules.maxLength)) {
        isValid = false;
        message = `Máximo ${rules.maxLength} caracteres`;
    }
    
    // Pattern
    if (rules.pattern && value && !new RegExp(rules.pattern).test(value)) {
        isValid = false;
        message = rules.patternMessage || 'Formato inválido';
    }
    
    // Atualizar UI
    this.updateFieldValidation(field, isValid, message);
    
    return isValid;
};

WoWSite.updateFieldValidation = function(field, isValid, message) {
    const feedback = field.parentNode.querySelector('.invalid-feedback') || 
                    this.createFeedbackElement(field);
    
    if (isValid) {
        field.classList.remove('is-invalid');
        field.classList.add('is-valid');
        feedback.style.display = 'none';
    } else {
        field.classList.remove('is-valid');
        field.classList.add('is-invalid');
        feedback.textContent = message;
        feedback.style.display = 'block';
    }
};

WoWSite.createFeedbackElement = function(field) {
    const feedback = document.createElement('div');
    feedback.className = 'invalid-feedback';
    field.parentNode.appendChild(feedback);
    return feedback;
};

WoWSite.handleFormSubmit = async function(form) {
    const formData = new FormData(form);
    const submitBtn = form.querySelector('[type="submit"]');
    const originalText = submitBtn.textContent;
    
    // Validar todos os campos
    const inputs = form.querySelectorAll('input, textarea, select');
    let isFormValid = true;
    
    inputs.forEach(input => {
        if (!this.validateField(input)) {
            isFormValid = false;
        }
    });
    
    if (!isFormValid) {
        this.utils.showNotification('Por favor, corrija os erros no formulário', 'danger');
        return;
    }
    
    // Mostrar loading
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<span class="loading-spinner"></span> Enviando...';
    
    try {
        const response = await this.utils.request(form.action, {
            method: 'POST',
            body: formData
        });
        
        if (response.success) {
            this.utils.showNotification(response.message || 'Sucesso!', 'success');
            form.reset();
            
            // Redirect se especificado
            if (response.redirect) {
                setTimeout(() => {
                    window.location.href = response.redirect;
                }, 1500);
            }
        } else {
            this.utils.showNotification(response.message || 'Erro ao processar', 'danger');
        }
        
    } catch (error) {
        this.utils.showNotification('Erro de conexão. Tente novamente.', 'danger');
    } finally {
        // Restaurar botão
        submitBtn.disabled = false;
        submitBtn.textContent = originalText;
    }
};

// ===== LAZY LOADING =====
WoWSite.initLazyLoading = function() {
    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src;
                    img.classList.remove('lazy');
                    imageObserver.unobserve(img);
                }
            });
        });
        
        document.querySelectorAll('img[data-src]').forEach(img => {
            imageObserver.observe(img);
        });
    }
};

// ===== UTILITÁRIOS GLOBAIS =====
window.WoWSite = WoWSite;

// Função global para copiar texto
window.copyText = function(text) {
    WoWSite.utils.copyToClipboard(text);
};

// Função global para mostrar notificação
window.showNotification = function(message, type, duration) {
    WoWSite.utils.showNotification(message, type, duration);
};

// ===== CSS ADICIONAL PARA ANIMAÇÕES =====
const style = document.createElement('style');
style.textContent = `
    .animate-on-scroll {
        opacity: 0;
        transform: translateY(30px);
        transition: all 0.6s ease;
    }
    
    .animate-on-scroll.animate-in {
        opacity: 1;
        transform: translateY(0);
    }
    
    .navbar-scrolled {
        background: rgba(26, 26, 46, 0.95) !important;
        backdrop-filter: blur(10px);
    }
    
    .lazy {
        filter: blur(5px);
        transition: filter 0.3s;
    }
`;
document.head.appendChild(style);
