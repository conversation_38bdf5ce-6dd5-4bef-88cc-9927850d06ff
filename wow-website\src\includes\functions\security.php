<?php
/**
 * Funções de Segurança
 * 
 * <AUTHOR> Agent
 * @version 1.0
 */

/**
 * Gerar token CSRF
 */
function generateCSRFToken() {
    if (!isset($_SESSION[CSRF_TOKEN_NAME])) {
        $_SESSION[CSRF_TOKEN_NAME] = bin2hex(random_bytes(32));
    }
    return $_SESSION[CSRF_TOKEN_NAME];
}

/**
 * Verificar token CSRF
 */
function verifyCSRFToken($token) {
    return isset($_SESSION[CSRF_TOKEN_NAME]) && 
           hash_equals($_SESSION[CSRF_TOKEN_NAME], $token);
}

/**
 * Sanitizar entrada de dados
 */
function sanitizeInput($input, $type = 'string') {
    if (is_array($input)) {
        return array_map(function($item) use ($type) {
            return sanitizeInput($item, $type);
        }, $input);
    }
    
    switch ($type) {
        case 'email':
            return filter_var(trim($input), FILTER_SANITIZE_EMAIL);
            
        case 'url':
            return filter_var(trim($input), FILTER_SANITIZE_URL);
            
        case 'int':
            return filter_var($input, FILTER_SANITIZE_NUMBER_INT);
            
        case 'float':
            return filter_var($input, FILTER_SANITIZE_NUMBER_FLOAT, FILTER_FLAG_ALLOW_FRACTION);
            
        case 'username':
            // Apenas letras, números e underscore
            return preg_replace('/[^a-zA-Z0-9_]/', '', trim($input));
            
        case 'html':
            return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
            
        case 'string':
        default:
            return htmlspecialchars(strip_tags(trim($input)), ENT_QUOTES, 'UTF-8');
    }
}

/**
 * Validar entrada de dados
 */
function validateInput($input, $rules) {
    $errors = [];
    
    foreach ($rules as $field => $rule) {
        $value = $input[$field] ?? null;
        
        // Required
        if (isset($rule['required']) && $rule['required'] && empty($value)) {
            $errors[$field] = "Campo {$field} é obrigatório";
            continue;
        }
        
        // Skip validation if field is empty and not required
        if (empty($value)) {
            continue;
        }
        
        // Min length
        if (isset($rule['min_length']) && strlen($value) < $rule['min_length']) {
            $errors[$field] = "Campo {$field} deve ter pelo menos {$rule['min_length']} caracteres";
        }
        
        // Max length
        if (isset($rule['max_length']) && strlen($value) > $rule['max_length']) {
            $errors[$field] = "Campo {$field} deve ter no máximo {$rule['max_length']} caracteres";
        }
        
        // Email
        if (isset($rule['email']) && $rule['email'] && !filter_var($value, FILTER_VALIDATE_EMAIL)) {
            $errors[$field] = "Campo {$field} deve ser um email válido";
        }
        
        // Pattern
        if (isset($rule['pattern']) && !preg_match($rule['pattern'], $value)) {
            $errors[$field] = $rule['pattern_message'] ?? "Campo {$field} tem formato inválido";
        }
        
        // Custom validation
        if (isset($rule['custom']) && is_callable($rule['custom'])) {
            $result = $rule['custom']($value);
            if ($result !== true) {
                $errors[$field] = $result;
            }
        }
    }
    
    return $errors;
}

/**
 * Hash de senha seguro
 */
function hashPassword($password) {
    return password_hash($password, PASSWORD_ARGON2ID, [
        'memory_cost' => 65536, // 64 MB
        'time_cost' => 4,       // 4 iterations
        'threads' => 3,         // 3 threads
    ]);
}

/**
 * Verificar senha
 */
function verifyPassword($password, $hash) {
    return password_verify($password, $hash);
}

/**
 * Gerar senha aleatória
 */
function generateRandomPassword($length = 12) {
    $chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
    return substr(str_shuffle(str_repeat($chars, ceil($length / strlen($chars)))), 0, $length);
}

/**
 * Rate limiting
 */
function checkRateLimit($identifier, $max_attempts = 10, $window = 3600) {
    $cache_key = "rate_limit_{$identifier}";
    $attempts = SimpleCache::get($cache_key) ?? 0;
    
    if ($attempts >= $max_attempts) {
        return false;
    }
    
    SimpleCache::set($cache_key, $attempts + 1, $window);
    return true;
}

/**
 * Verificar tentativas de login
 */
function checkLoginAttempts($username, $ip) {
    $cache_key_user = "login_attempts_user_{$username}";
    $cache_key_ip = "login_attempts_ip_{$ip}";
    
    $user_attempts = SimpleCache::get($cache_key_user) ?? 0;
    $ip_attempts = SimpleCache::get($cache_key_ip) ?? 0;
    
    if ($user_attempts >= MAX_LOGIN_ATTEMPTS || $ip_attempts >= MAX_LOGIN_ATTEMPTS * 2) {
        return false;
    }
    
    return true;
}

/**
 * Registrar tentativa de login falhada
 */
function recordFailedLogin($username, $ip) {
    $cache_key_user = "login_attempts_user_{$username}";
    $cache_key_ip = "login_attempts_ip_{$ip}";
    
    $user_attempts = SimpleCache::get($cache_key_user) ?? 0;
    $ip_attempts = SimpleCache::get($cache_key_ip) ?? 0;
    
    SimpleCache::set($cache_key_user, $user_attempts + 1, LOGIN_LOCKOUT_TIME);
    SimpleCache::set($cache_key_ip, $ip_attempts + 1, LOGIN_LOCKOUT_TIME);
    
    writeLog('WARNING', 'Failed login attempt', [
        'username' => $username,
        'ip' => $ip,
        'user_attempts' => $user_attempts + 1,
        'ip_attempts' => $ip_attempts + 1
    ]);
}

/**
 * Limpar tentativas de login após sucesso
 */
function clearLoginAttempts($username, $ip) {
    SimpleCache::delete("login_attempts_user_{$username}");
    SimpleCache::delete("login_attempts_ip_{$ip}");
}

/**
 * Verificar se usuário está logado
 */
function isLoggedIn() {
    return isset($_SESSION['user_id']) && 
           isset($_SESSION['username']) &&
           isset($_SESSION['login_time']) &&
           (time() - $_SESSION['login_time']) < SESSION_TIMEOUT;
}

/**
 * Verificar se usuário é admin
 */
function isAdmin() {
    return isLoggedIn() && 
           isset($_SESSION['user_level']) && 
           $_SESSION['user_level'] >= 3; // GM level
}

/**
 * Fazer logout
 */
function logout() {
    if (isset($_SESSION['username'])) {
        writeLog('INFO', 'User logged out', ['username' => $_SESSION['username']]);
    }
    
    session_unset();
    session_destroy();
    session_start();
}

/**
 * Verificar permissões de arquivo
 */
function checkFilePermissions($file) {
    if (!file_exists($file)) {
        return false;
    }
    
    $perms = fileperms($file);
    $owner_perms = ($perms & 0x01C0) >> 6;
    $group_perms = ($perms & 0x0038) >> 3;
    $other_perms = ($perms & 0x0007);
    
    // Verificar se outros usuários têm permissão de escrita
    if ($other_perms & 0x02) {
        writeLog('WARNING', 'File has world-writable permissions', ['file' => $file]);
        return false;
    }
    
    return true;
}

/**
 * Escapar output para HTML
 */
function escape($string) {
    return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}

/**
 * Verificar se request é AJAX
 */
function isAjaxRequest() {
    return isset($_SERVER['HTTP_X_REQUESTED_WITH']) && 
           strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
}

/**
 * Verificar se request é POST
 */
function isPostRequest() {
    return $_SERVER['REQUEST_METHOD'] === 'POST';
}

/**
 * Verificar se request é GET
 */
function isGetRequest() {
    return $_SERVER['REQUEST_METHOD'] === 'GET';
}

/**
 * Obter IP real do usuário
 */
function getRealIP() {
    $ip_keys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
    
    foreach ($ip_keys as $key) {
        if (isset($_SERVER[$key]) && !empty($_SERVER[$key])) {
            $ips = explode(',', $_SERVER[$key]);
            $ip = trim($ips[0]);
            
            if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                return $ip;
            }
        }
    }
    
    return $_SERVER['REMOTE_ADDR'] ?? 'unknown';
}

/**
 * Verificar se IP está em blacklist
 */
function isIPBlacklisted($ip) {
    // Lista de IPs bloqueados (pode ser movida para banco de dados)
    $blacklisted_ips = [
        // Adicionar IPs problemáticos aqui
    ];
    
    return in_array($ip, $blacklisted_ips);
}

/**
 * Middleware de segurança
 */
function securityMiddleware() {
    $ip = getRealIP();
    
    // Verificar IP blacklist
    if (isIPBlacklisted($ip)) {
        http_response_code(403);
        die('Access denied');
    }
    
    // Rate limiting global
    if (!checkRateLimit($ip, 1000, 3600)) { // 1000 requests por hora
        http_response_code(429);
        die('Rate limit exceeded');
    }
    
    // Verificar user agent suspeito
    $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
    $suspicious_agents = ['bot', 'crawler', 'spider', 'scraper'];
    
    foreach ($suspicious_agents as $agent) {
        if (stripos($user_agent, $agent) !== false) {
            writeLog('WARNING', 'Suspicious user agent detected', [
                'ip' => $ip,
                'user_agent' => $user_agent
            ]);
            break;
        }
    }
}

// Executar middleware de segurança
securityMiddleware();

?>
