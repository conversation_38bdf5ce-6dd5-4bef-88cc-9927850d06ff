<?php
/**
 * Configuração de Banco de Dados - AzerothCore
 * 
 * <AUTHOR> Agent
 * @version 1.0
 */

// Configurações de ambiente
$environment = $_ENV['APP_ENV'] ?? 'development';

// Carregar variáveis de ambiente do arquivo .env
if (file_exists(__DIR__ . '/../../.env')) {
    $env_lines = file(__DIR__ . '/../../.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($env_lines as $line) {
        if (strpos($line, '#') === 0) continue; // Ignorar comentários
        if (strpos($line, '=') !== false) {
            list($key, $value) = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value);
        }
    }
}

// Configurações por ambiente
$config = [
    'development' => [
        'host' => $_ENV['DB_HOST'] ?? 'localhost',
        'port' => $_ENV['DB_PORT'] ?? 3306,
        'username' => $_ENV['DB_USER'] ?? 'acore',
        'password' => $_ENV['DB_PASSWORD'] ?? 'acore',
        'charset' => 'utf8mb4'
    ],
    'production' => [
        'host' => $_ENV['DB_HOST'] ?? 'localhost',
        'port' => $_ENV['DB_PORT'] ?? 3306,
        'username' => $_ENV['DB_USER'] ?? 'acore',
        'password' => $_ENV['DB_PASSWORD'] ?? 'acore',
        'charset' => 'utf8mb4'
    ]
];

// Bancos de dados do AzerothCore
$databases = [
    'auth' => [
        'name' => 'acore_auth',
        'description' => 'Contas de usuário e autenticação'
    ],
    'characters' => [
        'name' => 'acore_characters',
        'description' => 'Personagens dos jogadores'
    ],
    'world' => [
        'name' => 'acore_world',
        'description' => 'Dados do mundo (NPCs, quests, itens)'
    ],
    'website' => [
        'name' => 'wow_website',
        'description' => 'Dados específicos do website'
    ]
];

// Configuração atual baseada no ambiente
$db_config = $config[$environment];

// Classe de conexão com banco
class DatabaseManager {
    private static $connections = [];
    private static $config;
    
    public static function init($config) {
        self::$config = $config;
    }
    
    /**
     * Obter conexão com banco específico
     */
    public static function getConnection($database = 'auth') {
        global $databases, $db_config;
        
        if (!isset(self::$connections[$database])) {
            try {
                $db_name = $databases[$database]['name'];
                $dsn = "mysql:host={$db_config['host']};port={$db_config['port']};dbname={$db_name};charset={$db_config['charset']}";
                
                $pdo = new PDO($dsn, $db_config['username'], $db_config['password'], [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                    PDO::ATTR_EMULATE_PREPARES => false,
                    PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES {$db_config['charset']}"
                ]);
                
                self::$connections[$database] = $pdo;
                
            } catch (PDOException $e) {
                error_log("Erro de conexão com banco $database: " . $e->getMessage());
                throw new Exception("Erro de conexão com banco de dados");
            }
        }
        
        return self::$connections[$database];
    }
    
    /**
     * Verificar se servidor está online - VERIFICAÇÃO REAL DA PORTA
     */
    public static function isServerOnline() {
        // VERIFICAÇÃO SIMPLES E REAL: Testar porta 8085 (WorldServer)
        $connection = @fsockopen('localhost', 8085, $errno, $errstr, 1);

        if ($connection) {
            fclose($connection);
            return true; // Porta aberta = Servidor ONLINE
        }

        return false; // Porta fechada = Servidor OFFLINE
    }

    /**
     * Obter jogadores online REAL usando comando do worldserver
     */
    private static function getRealOnlinePlayers() {
        // Se servidor estiver offline, retornar 0
        if (!self::isServerOnline()) {
            return 0;
        }

        try {
            // Verificar jogadores realmente conectados no banco
            $char_db = self::getConnection('characters');
            $stmt = $char_db->query("SELECT COUNT(*) as online FROM characters WHERE online = 1");
            $result = $stmt->fetch();

            return (int)$result['online'];

        } catch (Exception $e) {
            return 0;
        }
    }
    
    /**
     * Obter estatísticas do servidor - VERIFICAÇÃO REAL
     */
    public static function getServerStats() {
        try {
            // VERIFICAÇÃO REAL: Testar porta do WorldServer
            $server_online = self::isServerOnline();

            if (!$server_online) {
                // Servidor REALMENTE OFFLINE - retornar dados reais
                return [
                    'online' => false,
                    'players_online' => 0,
                    'total_accounts' => 0,
                    'total_characters' => 0,
                    'uptime' => '0d 0h 0m'
                ];
            }

            // Servidor ONLINE - obter dados reais do banco
            $auth_db = self::getConnection('auth');
            $char_db = self::getConnection('characters');

            // Total de contas (sempre disponível mesmo com servidor off)
            $stmt = $auth_db->query("SELECT COUNT(*) as total FROM account");
            $total_accounts = $stmt->fetch()['total'];

            // Total de personagens (sempre disponível)
            $stmt = $char_db->query("SELECT COUNT(*) as total FROM characters");
            $total_characters = $stmt->fetch()['total'];

            // Jogadores online REAIS (só se servidor estiver online)
            $online_players = self::getRealOnlinePlayers();

            // Uptime do servidor
            $uptime = self::getServerUptime();

            return [
                'online' => true,
                'players_online' => $online_players,
                'total_accounts' => $total_accounts,
                'total_characters' => $total_characters,
                'uptime' => $uptime,
                'server_start_time' => time() // Timestamp atual para cálculo no frontend
            ];

        } catch (Exception $e) {
            // Em caso de erro de conexão, servidor está offline
            return [
                'online' => false,
                'players_online' => 0,
                'total_accounts' => 0,
                'total_characters' => 0,
                'uptime' => '0d 0h 0m'
            ];
        }
    }
    
    /**
     * Calcular uptime REAL do servidor
     */
    private static function getServerUptime() {
        // Se servidor estiver offline, uptime é 0
        if (!self::isServerOnline()) {
            return '0d 0h 0m';
        }

        try {
            // MÉTODO REAL: Verificar uptime do processo worldserver
            $ps_output = shell_exec("ps -eo pid,etime,comm | grep worldserver | grep -v grep | head -1");

            if ($ps_output && trim($ps_output) !== '') {
                $parts = preg_split('/\s+/', trim($ps_output));
                if (count($parts) >= 2) {
                    $uptime = $parts[1];
                    return self::formatUptime($uptime);
                }
            }

            // Se não encontrou processo, servidor acabou de iniciar
            return '0d 0h 1m';

        } catch (Exception $e) {
            return '0d 0h 0m';
        }
    }

    /**
     * Formatar uptime REAL do comando ps
     */
    private static function formatUptime($ps_uptime) {
        // Formato do ps: 12:34:56 ou 1-12:34:56 ou 12:34 ou 00:00

        if (strpos($ps_uptime, '-') !== false) {
            // Formato: dias-horas:minutos:segundos (ex: 2-15:30:45)
            list($days, $time) = explode('-', $ps_uptime);
            $time_parts = explode(':', $time);
            $hours = (int)$time_parts[0];
            $minutes = (int)$time_parts[1];
            return sprintf('%dd %dh %dm', (int)$days, $hours, $minutes);
        } else {
            // Formato: horas:minutos:segundos ou minutos:segundos
            $time_parts = explode(':', $ps_uptime);

            if (count($time_parts) == 3) {
                // horas:minutos:segundos (ex: 15:30:45)
                $hours = (int)$time_parts[0];
                $minutes = (int)$time_parts[1];
                $seconds = (int)$time_parts[2];

                if ($hours > 0) {
                    return sprintf('0d %dh %dm', $hours, $minutes);
                } else if ($minutes > 0) {
                    return sprintf('0d 0h %dm', $minutes);
                } else if ($seconds > 0) {
                    return sprintf('0d 0h 0m');
                } else {
                    return '0d 0h 0m';
                }
            } else if (count($time_parts) == 2) {
                // minutos:segundos (ex: 30:45 = 30 minutos e 45 segundos)
                $minutes = (int)$time_parts[0];
                $seconds = (int)$time_parts[1];

                if ($minutes > 0) {
                    return sprintf('0d 0h %dm', $minutes);
                } else {
                    return '0d 0h 0m';
                }
            }
        }

        return '0d 0h 0m';
    }

    /**
     * Formatar uptime a partir de segundos
     */
    private static function formatUptimeFromSeconds($seconds) {
        $days = floor($seconds / 86400);
        $hours = floor(($seconds % 86400) / 3600);
        $minutes = floor(($seconds % 3600) / 60);

        return sprintf('%dd %dh %dm', $days, $hours, $minutes);
    }
    
    /**
     * Fechar todas as conexões
     */
    public static function closeAll() {
        self::$connections = [];
    }
}

// Inicializar o gerenciador
DatabaseManager::init($db_config);

// Configurações globais
define('DB_HOST', $db_config['host']);
define('DB_PORT', $db_config['port']);
define('DB_USER', $db_config['username']);
define('DB_PASS', $db_config['password']);
define('DB_CHARSET', $db_config['charset']);

// Nomes dos bancos
define('DB_AUTH', $databases['auth']['name']);
define('DB_CHARACTERS', $databases['characters']['name']);
define('DB_WORLD', $databases['world']['name']);
define('DB_WEBSITE', $databases['website']['name']);

?>
