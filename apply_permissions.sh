#!/bin/bash
set -e

echo "Aplicando permissões..."

# Caminhos completos para segurança
CHOWN=/bin/chown
CHMOD=/bin/chmod
FIND=/usr/bin/find
MKDIR=/bin/mkdir

# Diretório do site
WEB_DIR="/var/www/html"

# Usuário e grupo do servidor web
WEB_USER="www-data"

# Aplicar permissões
$CHOWN -R $WEB_USER:$WEB_USER $WEB_DIR
$CHMOD -R 755 $WEB_DIR
$FIND $WEB_DIR -type f -name '*.php' -exec $CHMOD 644 {} \;

# Criar e ajustar permissões de diretórios de upload e logs
$MKDIR -p $WEB_DIR/logs $WEB_DIR/uploads
$CHOWN -R $WEB_USER:$WEB_USER $WEB_DIR/logs $WEB_DIR/uploads
$CHMOD -R 755 $WEB_DIR/logs $WEB_DIR/uploads

echo "Permissões aplicadas com sucesso."
