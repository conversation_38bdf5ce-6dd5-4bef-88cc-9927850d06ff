<?php
/**
 * Teste de Cadastro - Simular POST
 * 
 * <AUTHOR> Agent
 * @version 1.0
 */

// Simular dados POST
$_POST = [
    'username' => 'testuser' . rand(100, 999),
    'email' => 'test' . rand(100, 999) . '@example.com',
    'password' => '123456',
    'confirm_password' => '123456',
    'csrf_token' => 'test_token'
];

$_SERVER['REQUEST_METHOD'] = 'POST';

// Incluir a página de registro
ob_start();
include 'public/register.php';
$output = ob_get_clean();

// Verificar resultado
if (strpos($output, 'sucesso') !== false) {
    echo "✅ CADASTRO FUNCIONOU!\n";
    echo "Username: " . $_POST['username'] . "\n";
    echo "Email: " . $_POST['email'] . "\n";
} else if (strpos($output, 'erro') !== false || strpos($output, 'alert-danger') !== false) {
    echo "❌ ERRO NO CADASTRO!\n";
    
    // Extrair erros
    preg_match_all('/<li>(.*?)<\/li>/', $output, $matches);
    if (!empty($matches[1])) {
        echo "Erros encontrados:\n";
        foreach ($matches[1] as $error) {
            echo "- " . strip_tags($error) . "\n";
        }
    }
} else {
    echo "⚠️ RESULTADO INDEFINIDO\n";
}

echo "\n--- DEBUG INFO ---\n";
echo "POST data: " . json_encode($_POST) . "\n";
echo "Output length: " . strlen($output) . " chars\n";

// Verificar se conta foi criada no banco
try {
    require_once 'includes/config/config.php';
    $auth_db = DatabaseManager::getConnection('auth');
    $stmt = $auth_db->prepare("SELECT id, username, email FROM account WHERE username = ?");
    $stmt->execute([$_POST['username']]);
    $account = $stmt->fetch();
    
    if ($account) {
        echo "✅ CONTA CRIADA NO BANCO!\n";
        echo "ID: " . $account['id'] . "\n";
        echo "Username: " . $account['username'] . "\n";
        echo "Email: " . $account['email'] . "\n";
    } else {
        echo "❌ CONTA NÃO ENCONTRADA NO BANCO\n";
    }
} catch (Exception $e) {
    echo "❌ ERRO AO VERIFICAR BANCO: " . $e->getMessage() . "\n";
}

?>
