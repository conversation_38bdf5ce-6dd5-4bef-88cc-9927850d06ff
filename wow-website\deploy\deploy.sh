#!/bin/bash

# ===== SCRIPT DE DEPLOY AUTOMATIZADO =====
# Deploy profissional para site WoW 3.3.5a
# 
# <AUTHOR> Agent
# @version 1.0

set -e  # Parar em caso de erro

# ===== CONFIGURAÇÕES =====
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
ENVIRONMENT=${1:-staging}

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# ===== FUNÇÕES =====
log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}✅ $1${NC}"
}

warning() {
    echo -e "${YELLOW}⚠️ $1${NC}"
}

error() {
    echo -e "${RED}❌ $1${NC}"
    exit 1
}

# ===== CONFIGURAÇÕES POR AMBIENTE =====
case $ENVIRONMENT in
    "development")
        SERVER_HOST="localhost"
        SERVER_USER="www-data"
        SERVER_PATH="/var/www/html/wow-dev"
        SSH_KEY=""
        ;;
    "staging")
        SERVER_HOST="************"  # IP do servidor Azeroth-Nexus
        SERVER_USER="root"
        SERVER_PATH="/var/www/html/wow-staging"
        SSH_KEY="$HOME/.ssh/azeroth-nexus-server"
        SSH_ALIAS="azeroth-nexus"
        ;;
    "production")
        SERVER_HOST="************"  # IP do servidor Azeroth-Nexus
        SERVER_USER="root"
        SERVER_PATH="/var/www/html"
        SSH_KEY="$HOME/.ssh/azeroth-nexus-server"
        SSH_ALIAS="azeroth-nexus"
        ;;
    *)
        error "Ambiente inválido: $ENVIRONMENT. Use: development, staging, production"
        ;;
esac

# ===== VERIFICAÇÕES INICIAIS =====
log "🚀 Iniciando deploy para ambiente: $ENVIRONMENT"

# Verificar se estamos no diretório correto
if [ ! -f "$PROJECT_DIR/src/public/index.php" ]; then
    error "Arquivo index.php não encontrado. Execute o script do diretório correto."
fi

# Verificar dependências
command -v rsync >/dev/null 2>&1 || error "rsync não está instalado"
command -v ssh >/dev/null 2>&1 || error "ssh não está instalado"

# ===== BUILD DOS ASSETS =====
log "📦 Preparando assets..."

# Criar diretório de build
BUILD_DIR="$PROJECT_DIR/build"
rm -rf "$BUILD_DIR"
mkdir -p "$BUILD_DIR"

# Copiar arquivos fonte
cp -r "$PROJECT_DIR/src/"* "$BUILD_DIR/"

# Minificar CSS (se tiver Node.js instalado)
if command -v npx >/dev/null 2>&1; then
    log "🎨 Minificando CSS..."
    # Aqui você pode adicionar minificação de CSS/JS
    # npx cleancss -o "$BUILD_DIR/public/assets/css/style.min.css" "$BUILD_DIR/public/assets/css/style.css"
fi

# Configurar permissões
find "$BUILD_DIR" -type f -exec chmod 644 {} \;
find "$BUILD_DIR" -type d -exec chmod 755 {} \;

success "Assets preparados"

# ===== CONFIGURAÇÕES DE AMBIENTE =====
log "⚙️ Configurando ambiente $ENVIRONMENT..."

# Criar arquivo .env
cat > "$BUILD_DIR/.env" << EOF
APP_ENV=$ENVIRONMENT
SITE_URL=https://seuservidor.com
DB_PASSWORD=sua_senha_aqui
SMTP_HOST=localhost
SMTP_PORT=587
SMTP_USER=
SMTP_PASS=
SMTP_FROM=<EMAIL>
PAYPAL_CLIENT_ID=
PAYPAL_SECRET=
PIX_KEY=
LOG_LEVEL=INFO
EOF

# Configurações específicas por ambiente
if [ "$ENVIRONMENT" = "production" ]; then
    # Remover arquivos de desenvolvimento
    rm -f "$BUILD_DIR/public/test.php"
    rm -rf "$BUILD_DIR/public/dev-tools/"
    
    # Configurar logs para produção
    sed -i 's/LOG_LEVEL=INFO/LOG_LEVEL=WARNING/' "$BUILD_DIR/.env"
fi

success "Ambiente configurado"

# ===== FUNÇÃO DE DEPLOY LOCAL =====
deploy_local() {
    log "📁 Deploy local para $SERVER_PATH..."
    
    # Criar diretório se não existir
    sudo mkdir -p "$SERVER_PATH"
    
    # Copiar arquivos
    sudo rsync -av --delete \
        --exclude='.git' \
        --exclude='node_modules' \
        --exclude='*.log' \
        "$BUILD_DIR/" "$SERVER_PATH/"
    
    # Configurar permissões
    sudo chown -R www-data:www-data "$SERVER_PATH"
    sudo chmod -R 755 "$SERVER_PATH"
    sudo chmod -R 644 "$SERVER_PATH"/*.php
    
    # Criar diretórios necessários
    sudo mkdir -p "$SERVER_PATH/logs" "$SERVER_PATH/uploads" "$SERVER_PATH/public/assets/images"
    sudo chown -R www-data:www-data "$SERVER_PATH/logs" "$SERVER_PATH/uploads" "$SERVER_PATH/public/assets/images"
    sudo chmod 755 "$SERVER_PATH/logs" "$SERVER_PATH/uploads" "$SERVER_PATH/public/assets/images"
}

# ===== FUNÇÃO DE DEPLOY REMOTO =====
deploy_remote() {
    log "🌐 Deploy remoto para $SERVER_HOST..."
    
    # Testar conexão SSH usando alias primeiro (prioridade)
    log "🔑 Testando conexão SSH..."
    
    # Usar a chave SSH correta (sem senha) - agora limpa e funcionando
    SSH_KEY_PATH="/mnt/c/Users/<USER>/.ssh/azeroth-nexus-server"

    # Verificar se a chave existe
    if [ ! -f "$SSH_KEY_PATH" ]; then
        error "Chave SSH não encontrada: $SSH_KEY_PATH"
        return 1
    fi

    # USAR POWERSHELL SSH QUE FUNCIONA PERFEITAMENTE
    success "Usando PowerShell SSH (funciona sem senha)"

    # Definir comandos que usam PowerShell em vez de bash SSH
    SSH_CONNECTION="powershell.exe -Command ssh azeroth-nexus"
    RSYNC_COMMAND="powershell.exe -Command"

    # Teste rápido de conectividade
    log "🔑 Testando conectividade via PowerShell..."
    if ! powershell.exe -Command "ssh azeroth-nexus 'echo SSH-OK'" | grep -q "SSH-OK"; then
        error "Falha na conexão SSH via PowerShell"
        return 1
    fi
    success "Conexão SSH confirmada via PowerShell"
    
    # Fazer backup do site atual (ETAPA DESATIVADA PARA ACELERAR O PROCESSO)
    log "💾 Pulando a etapa de backup do site atual..."
    # ssh -i "$SSH_KEY" "$SERVER_USER@$SERVER_HOST" "
    #     if [ -d '$SERVER_PATH' ]; then
    #         sudo tar -czf /tmp/website-backup-$(date +%Y%m%d_%H%M%S).tar.gz -C '$SERVER_PATH' . || true
    #     fi
    # "
    
    # Upload dos arquivos usando PowerShell SCP
    log "📤 Enviando arquivos via PowerShell..."

    # Converter caminho para Windows
    BUILD_DIR_WIN=$(echo "$BUILD_DIR" | sed 's|/mnt/c|C:|' | sed 's|/|\\|g')

    # Usar PowerShell para fazer upload
    powershell.exe -Command "
        Write-Host 'Fazendo upload dos arquivos...'
        scp -r '$BUILD_DIR_WIN\\*' azeroth-nexus:$SERVER_PATH/
    "

    # Verificar se o upload funcionou
    log "🖼️ Verificando upload..."
    powershell.exe -Command "ssh azeroth-nexus 'ls -la $SERVER_PATH/ | head -5'"

    # Configurar permissões remotamente
    log "🔐 Configurando permissões..."
    powershell.exe -Command "ssh azeroth-nexus '
        sudo chown -R www-data:www-data $SERVER_PATH
        sudo chmod -R 755 $SERVER_PATH
        sudo find $SERVER_PATH -name \"*.php\" -exec chmod 644 {} \;

        # Criar diretórios necessários
        sudo mkdir -p $SERVER_PATH/logs $SERVER_PATH/uploads $SERVER_PATH/public/assets/images
        sudo chown -R www-data:www-data $SERVER_PATH/logs $SERVER_PATH/uploads $SERVER_PATH/public/assets/images
        sudo chmod 755 $SERVER_PATH/logs $SERVER_PATH/uploads $SERVER_PATH/public/assets/images
    '"
}

# ===== CONFIGURAR NGINX =====
configure_nginx() {
    log "🌐 Configurando Nginx..."
    
    local nginx_config="$SCRIPT_DIR/nginx.conf"
    local target_config="/etc/nginx/sites-available/wow-site"
    
    if [ "$ENVIRONMENT" = "development" ]; then
        # Deploy local
        sudo cp "$nginx_config" "$target_config"
        sudo sed -i "s|SERVER_NAME|localhost|g" "$target_config"
        sudo sed -i "s|DOCUMENT_ROOT|$SERVER_PATH|g" "$target_config"
        
        # Ativar site
        sudo ln -sf "$target_config" "/etc/nginx/sites-enabled/"
        sudo nginx -t && sudo systemctl reload nginx
        
    else
        # Deploy remoto (será configurado após container estar pronto)
        warning "Configuração do Nginx será feita após o container estar disponível"
    fi
}

# ===== CONFIGURAR PHP-FPM =====
configure_php() {
    log "🐘 Configurando PHP-FPM..."
    
    if [ "$ENVIRONMENT" = "development" ]; then
        # Configurações locais de PHP
        local php_config="$SCRIPT_DIR/php-fpm.conf"
        if [ -f "$php_config" ]; then
            sudo cp "$php_config" "/etc/php/8.1/fpm/pool.d/wow-site.conf"
            sudo systemctl reload php8.1-fpm
        fi
    fi
}

# ===== VERIFICAÇÕES PÓS-DEPLOY =====
verify_deployment() {
    log "🔍 Verificando deployment..."
    
    if [ "$ENVIRONMENT" = "development" ]; then
        # Verificar se o site está acessível
        if curl -s -o /dev/null -w "%{\http_code}" "http://localhost" | grep -q "200"; then
            success "Site acessível em http://localhost"
        else
            warning "Site pode não estar acessível. Verifique a configuração do Nginx."
        fi
    else
        warning "Verificação remota será feita após container estar disponível"
    fi
    
    # Verificar estrutura de arquivos
    local check_files=(
        "public/index.php"
        "includes/config/config.php"
        "includes/config/database.php"
        ".env"
    )
    
    for file in "${check_files[@]}"; do
        if [ -f "$BUILD_DIR/$file" ]; then
            success "Arquivo encontrado: $file"
        else
            error "Arquivo não encontrado: $file"
        fi
    done
}

# ===== EXECUÇÃO PRINCIPAL =====
main() {
    log "🎯 Executando deploy para $ENVIRONMENT..."
    
    # Deploy baseado no ambiente
    if [ "$ENVIRONMENT" = "development" ]; then
        deploy_local
        configure_nginx
        configure_php
    else
        deploy_remote
    fi
    
    # Verificações
    verify_deployment
    
    # Limpeza
    log "🧹 Limpando arquivos temporários..."
    rm -rf "$BUILD_DIR"
    
    success "Deploy concluído com sucesso!"
    
    # Informações finais
    echo ""
    echo "📋 Informações do Deploy:"
    echo "   • Ambiente: $ENVIRONMENT"
    echo "   • Servidor: $SERVER_HOST"
    echo "   • Caminho: $SERVER_PATH"
    echo "   • Data: $(date '+%Y-%m-%d %H:%M:%S')"
    echo ""
    
    if [ "$ENVIRONMENT" = "development" ]; then
        echo "🌐 Site disponível em: http://localhost"
    else
        echo "✅ SSH configurado para usar:"
        echo "   • Alias: $SSH_ALIAS"
        echo "   • Chave: $SSH_KEY"
        echo "   • Teste: ssh $SSH_ALIAS"
        echo ""
        echo "📖 Para mais informações sobre SSH, consulte: docs/ssh-sem-senha.md"
    fi
}

# ===== TRATAMENTO DE SINAIS =====
trap 'error "Deploy interrompido pelo usuário"' INT TERM

# ===== EXECUÇÃO =====
if [ "${BASH_SOURCE[0]}" = "${0}" ]; then
    main "$@"
fi