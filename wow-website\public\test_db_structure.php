<?php
/**
 * Teste de Estrutura do Banco de Dados
 * 
 * <AUTHOR> Agent
 * @version 1.0
 */

require_once '../includes/config/config.php';

echo "<h2>🔍 Teste de Estrutura do Banco de Dados</h2>\n";

try {
    // Conectar ao banco auth
    $auth_db = DatabaseManager::getConnection('auth');
    
    echo "<h3>📋 Estrutura da tabela 'account':</h3>\n";
    
    // Verificar estrutura da tabela account
    $stmt = $auth_db->prepare("DESCRIBE account");
    $stmt->execute();
    $columns = $stmt->fetchAll();
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>\n";
    echo "<tr><th>Campo</th><th>Tipo</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>\n";
    
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($column['Field']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Type']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Null']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Key']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Default']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Extra']) . "</td>";
        echo "</tr>\n";
    }
    echo "</table>\n";
    
    // Verificar se existem contas de teste
    echo "<h3>📊 Contas existentes (apenas contagem):</h3>\n";
    $stmt = $auth_db->prepare("SELECT COUNT(*) as total FROM account");
    $stmt->execute();
    $result = $stmt->fetch();
    echo "<p>Total de contas: " . $result['total'] . "</p>\n";
    
    // Verificar campos de senha disponíveis
    echo "<h3>🔐 Campos de autenticação disponíveis:</h3>\n";
    $auth_fields = [];
    foreach ($columns as $column) {
        $field = $column['Field'];
        if (in_array($field, ['sha_pass_hash', 'salt', 'verifier', 'password'])) {
            $auth_fields[] = $field;
        }
    }
    
    if (empty($auth_fields)) {
        echo "<p style='color: red;'>❌ Nenhum campo de autenticação encontrado!</p>\n";
    } else {
        echo "<ul>\n";
        foreach ($auth_fields as $field) {
            echo "<li>✅ " . htmlspecialchars($field) . "</li>\n";
        }
        echo "</ul>\n";
    }
    
    // Verificar configuração do banco
    echo "<h3>⚙️ Configuração do banco:</h3>\n";
    echo "<p>Host: " . htmlspecialchars($db_config['host']) . "</p>\n";
    echo "<p>Porta: " . htmlspecialchars($db_config['port']) . "</p>\n";
    echo "<p>Usuário: " . htmlspecialchars($db_config['username']) . "</p>\n";
    echo "<p>Banco auth: " . htmlspecialchars($databases['auth']['name']) . "</p>\n";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Erro ao conectar com banco: " . htmlspecialchars($e->getMessage()) . "</p>\n";
    
    // Tentar diagnóstico adicional
    echo "<h3>🔧 Diagnóstico:</h3>\n";
    echo "<ul>\n";
    echo "<li>Verifique se o MySQL está rodando</li>\n";
    echo "<li>Verifique as credenciais no arquivo de configuração</li>\n";
    echo "<li>Verifique se o banco 'acore_auth' existe</li>\n";
    echo "</ul>\n";
}

echo "<hr>\n";
echo "<p><a href='login.php'>← Voltar para Login</a></p>\n";
?>
